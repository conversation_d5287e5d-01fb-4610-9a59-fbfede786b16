/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/typedarray";
exports.ids = ["vendor-chunks/typedarray"];
exports.modules = {

/***/ "(rsc)/./node_modules/typedarray/index.js":
/*!******************************************!*\
  !*** ./node_modules/typedarray/index.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("var undefined = (void 0); // Paranoia\n\n// Beyond this value, index getters/setters (i.e. array[0], array[1]) are so slow to\n// create, and consume so much memory, that the browser appears frozen.\nvar MAX_ARRAY_LENGTH = 1e5;\n\n// Approximations of internal ECMAScript conversion functions\nvar ECMAScript = (function() {\n  // Stash a copy in case other scripts modify these\n  var opts = Object.prototype.toString,\n      ophop = Object.prototype.hasOwnProperty;\n\n  return {\n    // Class returns internal [[Class]] property, used to avoid cross-frame instanceof issues:\n    Class: function(v) { return opts.call(v).replace(/^\\[object *|\\]$/g, ''); },\n    HasProperty: function(o, p) { return p in o; },\n    HasOwnProperty: function(o, p) { return ophop.call(o, p); },\n    IsCallable: function(o) { return typeof o === 'function'; },\n    ToInt32: function(v) { return v >> 0; },\n    ToUint32: function(v) { return v >>> 0; }\n  };\n}());\n\n// Snapshot intrinsics\nvar LN2 = Math.LN2,\n    abs = Math.abs,\n    floor = Math.floor,\n    log = Math.log,\n    min = Math.min,\n    pow = Math.pow,\n    round = Math.round;\n\n// ES5: lock down object properties\nfunction configureProperties(obj) {\n  if (getOwnPropNames && defineProp) {\n    var props = getOwnPropNames(obj), i;\n    for (i = 0; i < props.length; i += 1) {\n      defineProp(obj, props[i], {\n        value: obj[props[i]],\n        writable: false,\n        enumerable: false,\n        configurable: false\n      });\n    }\n  }\n}\n\n// emulate ES5 getter/setter API using legacy APIs\n// http://blogs.msdn.com/b/ie/archive/2010/09/07/transitioning-existing-code-to-the-es5-getter-setter-apis.aspx\n// (second clause tests for Object.defineProperty() in IE<9 that only supports extending DOM prototypes, but\n// note that IE<9 does not support __defineGetter__ or __defineSetter__ so it just renders the method harmless)\nvar defineProp\nif (Object.defineProperty && (function() {\n      try {\n        Object.defineProperty({}, 'x', {});\n        return true;\n      } catch (e) {\n        return false;\n      }\n    })()) {\n  defineProp = Object.defineProperty;\n} else {\n  defineProp = function(o, p, desc) {\n    if (!o === Object(o)) throw new TypeError(\"Object.defineProperty called on non-object\");\n    if (ECMAScript.HasProperty(desc, 'get') && Object.prototype.__defineGetter__) { Object.prototype.__defineGetter__.call(o, p, desc.get); }\n    if (ECMAScript.HasProperty(desc, 'set') && Object.prototype.__defineSetter__) { Object.prototype.__defineSetter__.call(o, p, desc.set); }\n    if (ECMAScript.HasProperty(desc, 'value')) { o[p] = desc.value; }\n    return o;\n  };\n}\n\nvar getOwnPropNames = Object.getOwnPropertyNames || function (o) {\n  if (o !== Object(o)) throw new TypeError(\"Object.getOwnPropertyNames called on non-object\");\n  var props = [], p;\n  for (p in o) {\n    if (ECMAScript.HasOwnProperty(o, p)) {\n      props.push(p);\n    }\n  }\n  return props;\n};\n\n// ES5: Make obj[index] an alias for obj._getter(index)/obj._setter(index, value)\n// for index in 0 ... obj.length\nfunction makeArrayAccessors(obj) {\n  if (!defineProp) { return; }\n\n  if (obj.length > MAX_ARRAY_LENGTH) throw new RangeError(\"Array too large for polyfill\");\n\n  function makeArrayAccessor(index) {\n    defineProp(obj, index, {\n      'get': function() { return obj._getter(index); },\n      'set': function(v) { obj._setter(index, v); },\n      enumerable: true,\n      configurable: false\n    });\n  }\n\n  var i;\n  for (i = 0; i < obj.length; i += 1) {\n    makeArrayAccessor(i);\n  }\n}\n\n// Internal conversion functions:\n//    pack<Type>()   - take a number (interpreted as Type), output a byte array\n//    unpack<Type>() - take a byte array, output a Type-like number\n\nfunction as_signed(value, bits) { var s = 32 - bits; return (value << s) >> s; }\nfunction as_unsigned(value, bits) { var s = 32 - bits; return (value << s) >>> s; }\n\nfunction packI8(n) { return [n & 0xff]; }\nfunction unpackI8(bytes) { return as_signed(bytes[0], 8); }\n\nfunction packU8(n) { return [n & 0xff]; }\nfunction unpackU8(bytes) { return as_unsigned(bytes[0], 8); }\n\nfunction packU8Clamped(n) { n = round(Number(n)); return [n < 0 ? 0 : n > 0xff ? 0xff : n & 0xff]; }\n\nfunction packI16(n) { return [(n >> 8) & 0xff, n & 0xff]; }\nfunction unpackI16(bytes) { return as_signed(bytes[0] << 8 | bytes[1], 16); }\n\nfunction packU16(n) { return [(n >> 8) & 0xff, n & 0xff]; }\nfunction unpackU16(bytes) { return as_unsigned(bytes[0] << 8 | bytes[1], 16); }\n\nfunction packI32(n) { return [(n >> 24) & 0xff, (n >> 16) & 0xff, (n >> 8) & 0xff, n & 0xff]; }\nfunction unpackI32(bytes) { return as_signed(bytes[0] << 24 | bytes[1] << 16 | bytes[2] << 8 | bytes[3], 32); }\n\nfunction packU32(n) { return [(n >> 24) & 0xff, (n >> 16) & 0xff, (n >> 8) & 0xff, n & 0xff]; }\nfunction unpackU32(bytes) { return as_unsigned(bytes[0] << 24 | bytes[1] << 16 | bytes[2] << 8 | bytes[3], 32); }\n\nfunction packIEEE754(v, ebits, fbits) {\n\n  var bias = (1 << (ebits - 1)) - 1,\n      s, e, f, ln,\n      i, bits, str, bytes;\n\n  function roundToEven(n) {\n    var w = floor(n), f = n - w;\n    if (f < 0.5)\n      return w;\n    if (f > 0.5)\n      return w + 1;\n    return w % 2 ? w + 1 : w;\n  }\n\n  // Compute sign, exponent, fraction\n  if (v !== v) {\n    // NaN\n    // http://dev.w3.org/2006/webapi/WebIDL/#es-type-mapping\n    e = (1 << ebits) - 1; f = pow(2, fbits - 1); s = 0;\n  } else if (v === Infinity || v === -Infinity) {\n    e = (1 << ebits) - 1; f = 0; s = (v < 0) ? 1 : 0;\n  } else if (v === 0) {\n    e = 0; f = 0; s = (1 / v === -Infinity) ? 1 : 0;\n  } else {\n    s = v < 0;\n    v = abs(v);\n\n    if (v >= pow(2, 1 - bias)) {\n      e = min(floor(log(v) / LN2), 1023);\n      f = roundToEven(v / pow(2, e) * pow(2, fbits));\n      if (f / pow(2, fbits) >= 2) {\n        e = e + 1;\n        f = 1;\n      }\n      if (e > bias) {\n        // Overflow\n        e = (1 << ebits) - 1;\n        f = 0;\n      } else {\n        // Normalized\n        e = e + bias;\n        f = f - pow(2, fbits);\n      }\n    } else {\n      // Denormalized\n      e = 0;\n      f = roundToEven(v / pow(2, 1 - bias - fbits));\n    }\n  }\n\n  // Pack sign, exponent, fraction\n  bits = [];\n  for (i = fbits; i; i -= 1) { bits.push(f % 2 ? 1 : 0); f = floor(f / 2); }\n  for (i = ebits; i; i -= 1) { bits.push(e % 2 ? 1 : 0); e = floor(e / 2); }\n  bits.push(s ? 1 : 0);\n  bits.reverse();\n  str = bits.join('');\n\n  // Bits to bytes\n  bytes = [];\n  while (str.length) {\n    bytes.push(parseInt(str.substring(0, 8), 2));\n    str = str.substring(8);\n  }\n  return bytes;\n}\n\nfunction unpackIEEE754(bytes, ebits, fbits) {\n\n  // Bytes to bits\n  var bits = [], i, j, b, str,\n      bias, s, e, f;\n\n  for (i = bytes.length; i; i -= 1) {\n    b = bytes[i - 1];\n    for (j = 8; j; j -= 1) {\n      bits.push(b % 2 ? 1 : 0); b = b >> 1;\n    }\n  }\n  bits.reverse();\n  str = bits.join('');\n\n  // Unpack sign, exponent, fraction\n  bias = (1 << (ebits - 1)) - 1;\n  s = parseInt(str.substring(0, 1), 2) ? -1 : 1;\n  e = parseInt(str.substring(1, 1 + ebits), 2);\n  f = parseInt(str.substring(1 + ebits), 2);\n\n  // Produce number\n  if (e === (1 << ebits) - 1) {\n    return f !== 0 ? NaN : s * Infinity;\n  } else if (e > 0) {\n    // Normalized\n    return s * pow(2, e - bias) * (1 + f / pow(2, fbits));\n  } else if (f !== 0) {\n    // Denormalized\n    return s * pow(2, -(bias - 1)) * (f / pow(2, fbits));\n  } else {\n    return s < 0 ? -0 : 0;\n  }\n}\n\nfunction unpackF64(b) { return unpackIEEE754(b, 11, 52); }\nfunction packF64(v) { return packIEEE754(v, 11, 52); }\nfunction unpackF32(b) { return unpackIEEE754(b, 8, 23); }\nfunction packF32(v) { return packIEEE754(v, 8, 23); }\n\n\n//\n// 3 The ArrayBuffer Type\n//\n\n(function() {\n\n  /** @constructor */\n  var ArrayBuffer = function ArrayBuffer(length) {\n    length = ECMAScript.ToInt32(length);\n    if (length < 0) throw new RangeError('ArrayBuffer size is not a small enough positive integer');\n\n    this.byteLength = length;\n    this._bytes = [];\n    this._bytes.length = length;\n\n    var i;\n    for (i = 0; i < this.byteLength; i += 1) {\n      this._bytes[i] = 0;\n    }\n\n    configureProperties(this);\n  };\n\n  exports.ArrayBuffer = exports.ArrayBuffer || ArrayBuffer;\n\n  //\n  // 4 The ArrayBufferView Type\n  //\n\n  // NOTE: this constructor is not exported\n  /** @constructor */\n  var ArrayBufferView = function ArrayBufferView() {\n    //this.buffer = null;\n    //this.byteOffset = 0;\n    //this.byteLength = 0;\n  };\n\n  //\n  // 5 The Typed Array View Types\n  //\n\n  function makeConstructor(bytesPerElement, pack, unpack) {\n    // Each TypedArray type requires a distinct constructor instance with\n    // identical logic, which this produces.\n\n    var ctor;\n    ctor = function(buffer, byteOffset, length) {\n      var array, sequence, i, s;\n\n      if (!arguments.length || typeof arguments[0] === 'number') {\n        // Constructor(unsigned long length)\n        this.length = ECMAScript.ToInt32(arguments[0]);\n        if (length < 0) throw new RangeError('ArrayBufferView size is not a small enough positive integer');\n\n        this.byteLength = this.length * this.BYTES_PER_ELEMENT;\n        this.buffer = new ArrayBuffer(this.byteLength);\n        this.byteOffset = 0;\n      } else if (typeof arguments[0] === 'object' && arguments[0].constructor === ctor) {\n        // Constructor(TypedArray array)\n        array = arguments[0];\n\n        this.length = array.length;\n        this.byteLength = this.length * this.BYTES_PER_ELEMENT;\n        this.buffer = new ArrayBuffer(this.byteLength);\n        this.byteOffset = 0;\n\n        for (i = 0; i < this.length; i += 1) {\n          this._setter(i, array._getter(i));\n        }\n      } else if (typeof arguments[0] === 'object' &&\n                 !(arguments[0] instanceof ArrayBuffer || ECMAScript.Class(arguments[0]) === 'ArrayBuffer')) {\n        // Constructor(sequence<type> array)\n        sequence = arguments[0];\n\n        this.length = ECMAScript.ToUint32(sequence.length);\n        this.byteLength = this.length * this.BYTES_PER_ELEMENT;\n        this.buffer = new ArrayBuffer(this.byteLength);\n        this.byteOffset = 0;\n\n        for (i = 0; i < this.length; i += 1) {\n          s = sequence[i];\n          this._setter(i, Number(s));\n        }\n      } else if (typeof arguments[0] === 'object' &&\n                 (arguments[0] instanceof ArrayBuffer || ECMAScript.Class(arguments[0]) === 'ArrayBuffer')) {\n        // Constructor(ArrayBuffer buffer,\n        //             optional unsigned long byteOffset, optional unsigned long length)\n        this.buffer = buffer;\n\n        this.byteOffset = ECMAScript.ToUint32(byteOffset);\n        if (this.byteOffset > this.buffer.byteLength) {\n          throw new RangeError(\"byteOffset out of range\");\n        }\n\n        if (this.byteOffset % this.BYTES_PER_ELEMENT) {\n          // The given byteOffset must be a multiple of the element\n          // size of the specific type, otherwise an exception is raised.\n          throw new RangeError(\"ArrayBuffer length minus the byteOffset is not a multiple of the element size.\");\n        }\n\n        if (arguments.length < 3) {\n          this.byteLength = this.buffer.byteLength - this.byteOffset;\n\n          if (this.byteLength % this.BYTES_PER_ELEMENT) {\n            throw new RangeError(\"length of buffer minus byteOffset not a multiple of the element size\");\n          }\n          this.length = this.byteLength / this.BYTES_PER_ELEMENT;\n        } else {\n          this.length = ECMAScript.ToUint32(length);\n          this.byteLength = this.length * this.BYTES_PER_ELEMENT;\n        }\n\n        if ((this.byteOffset + this.byteLength) > this.buffer.byteLength) {\n          throw new RangeError(\"byteOffset and length reference an area beyond the end of the buffer\");\n        }\n      } else {\n        throw new TypeError(\"Unexpected argument type(s)\");\n      }\n\n      this.constructor = ctor;\n\n      configureProperties(this);\n      makeArrayAccessors(this);\n    };\n\n    ctor.prototype = new ArrayBufferView();\n    ctor.prototype.BYTES_PER_ELEMENT = bytesPerElement;\n    ctor.prototype._pack = pack;\n    ctor.prototype._unpack = unpack;\n    ctor.BYTES_PER_ELEMENT = bytesPerElement;\n\n    // getter type (unsigned long index);\n    ctor.prototype._getter = function(index) {\n      if (arguments.length < 1) throw new SyntaxError(\"Not enough arguments\");\n\n      index = ECMAScript.ToUint32(index);\n      if (index >= this.length) {\n        return undefined;\n      }\n\n      var bytes = [], i, o;\n      for (i = 0, o = this.byteOffset + index * this.BYTES_PER_ELEMENT;\n           i < this.BYTES_PER_ELEMENT;\n           i += 1, o += 1) {\n        bytes.push(this.buffer._bytes[o]);\n      }\n      return this._unpack(bytes);\n    };\n\n    // NONSTANDARD: convenience alias for getter: type get(unsigned long index);\n    ctor.prototype.get = ctor.prototype._getter;\n\n    // setter void (unsigned long index, type value);\n    ctor.prototype._setter = function(index, value) {\n      if (arguments.length < 2) throw new SyntaxError(\"Not enough arguments\");\n\n      index = ECMAScript.ToUint32(index);\n      if (index >= this.length) {\n        return undefined;\n      }\n\n      var bytes = this._pack(value), i, o;\n      for (i = 0, o = this.byteOffset + index * this.BYTES_PER_ELEMENT;\n           i < this.BYTES_PER_ELEMENT;\n           i += 1, o += 1) {\n        this.buffer._bytes[o] = bytes[i];\n      }\n    };\n\n    // void set(TypedArray array, optional unsigned long offset);\n    // void set(sequence<type> array, optional unsigned long offset);\n    ctor.prototype.set = function(index, value) {\n      if (arguments.length < 1) throw new SyntaxError(\"Not enough arguments\");\n      var array, sequence, offset, len,\n          i, s, d,\n          byteOffset, byteLength, tmp;\n\n      if (typeof arguments[0] === 'object' && arguments[0].constructor === this.constructor) {\n        // void set(TypedArray array, optional unsigned long offset);\n        array = arguments[0];\n        offset = ECMAScript.ToUint32(arguments[1]);\n\n        if (offset + array.length > this.length) {\n          throw new RangeError(\"Offset plus length of array is out of range\");\n        }\n\n        byteOffset = this.byteOffset + offset * this.BYTES_PER_ELEMENT;\n        byteLength = array.length * this.BYTES_PER_ELEMENT;\n\n        if (array.buffer === this.buffer) {\n          tmp = [];\n          for (i = 0, s = array.byteOffset; i < byteLength; i += 1, s += 1) {\n            tmp[i] = array.buffer._bytes[s];\n          }\n          for (i = 0, d = byteOffset; i < byteLength; i += 1, d += 1) {\n            this.buffer._bytes[d] = tmp[i];\n          }\n        } else {\n          for (i = 0, s = array.byteOffset, d = byteOffset;\n               i < byteLength; i += 1, s += 1, d += 1) {\n            this.buffer._bytes[d] = array.buffer._bytes[s];\n          }\n        }\n      } else if (typeof arguments[0] === 'object' && typeof arguments[0].length !== 'undefined') {\n        // void set(sequence<type> array, optional unsigned long offset);\n        sequence = arguments[0];\n        len = ECMAScript.ToUint32(sequence.length);\n        offset = ECMAScript.ToUint32(arguments[1]);\n\n        if (offset + len > this.length) {\n          throw new RangeError(\"Offset plus length of array is out of range\");\n        }\n\n        for (i = 0; i < len; i += 1) {\n          s = sequence[i];\n          this._setter(offset + i, Number(s));\n        }\n      } else {\n        throw new TypeError(\"Unexpected argument type(s)\");\n      }\n    };\n\n    // TypedArray subarray(long begin, optional long end);\n    ctor.prototype.subarray = function(start, end) {\n      function clamp(v, min, max) { return v < min ? min : v > max ? max : v; }\n\n      start = ECMAScript.ToInt32(start);\n      end = ECMAScript.ToInt32(end);\n\n      if (arguments.length < 1) { start = 0; }\n      if (arguments.length < 2) { end = this.length; }\n\n      if (start < 0) { start = this.length + start; }\n      if (end < 0) { end = this.length + end; }\n\n      start = clamp(start, 0, this.length);\n      end = clamp(end, 0, this.length);\n\n      var len = end - start;\n      if (len < 0) {\n        len = 0;\n      }\n\n      return new this.constructor(\n        this.buffer, this.byteOffset + start * this.BYTES_PER_ELEMENT, len);\n    };\n\n    return ctor;\n  }\n\n  var Int8Array = makeConstructor(1, packI8, unpackI8);\n  var Uint8Array = makeConstructor(1, packU8, unpackU8);\n  var Uint8ClampedArray = makeConstructor(1, packU8Clamped, unpackU8);\n  var Int16Array = makeConstructor(2, packI16, unpackI16);\n  var Uint16Array = makeConstructor(2, packU16, unpackU16);\n  var Int32Array = makeConstructor(4, packI32, unpackI32);\n  var Uint32Array = makeConstructor(4, packU32, unpackU32);\n  var Float32Array = makeConstructor(4, packF32, unpackF32);\n  var Float64Array = makeConstructor(8, packF64, unpackF64);\n\n  exports.Int8Array = exports.Int8Array || Int8Array;\n  exports.Uint8Array = exports.Uint8Array || Uint8Array;\n  exports.Uint8ClampedArray = exports.Uint8ClampedArray || Uint8ClampedArray;\n  exports.Int16Array = exports.Int16Array || Int16Array;\n  exports.Uint16Array = exports.Uint16Array || Uint16Array;\n  exports.Int32Array = exports.Int32Array || Int32Array;\n  exports.Uint32Array = exports.Uint32Array || Uint32Array;\n  exports.Float32Array = exports.Float32Array || Float32Array;\n  exports.Float64Array = exports.Float64Array || Float64Array;\n}());\n\n//\n// 6 The DataView View Type\n//\n\n(function() {\n  function r(array, index) {\n    return ECMAScript.IsCallable(array.get) ? array.get(index) : array[index];\n  }\n\n  var IS_BIG_ENDIAN = (function() {\n    var u16array = new(exports.Uint16Array)([0x1234]),\n        u8array = new(exports.Uint8Array)(u16array.buffer);\n    return r(u8array, 0) === 0x12;\n  }());\n\n  // Constructor(ArrayBuffer buffer,\n  //             optional unsigned long byteOffset,\n  //             optional unsigned long byteLength)\n  /** @constructor */\n  var DataView = function DataView(buffer, byteOffset, byteLength) {\n    if (arguments.length === 0) {\n      buffer = new exports.ArrayBuffer(0);\n    } else if (!(buffer instanceof exports.ArrayBuffer || ECMAScript.Class(buffer) === 'ArrayBuffer')) {\n      throw new TypeError(\"TypeError\");\n    }\n\n    this.buffer = buffer || new exports.ArrayBuffer(0);\n\n    this.byteOffset = ECMAScript.ToUint32(byteOffset);\n    if (this.byteOffset > this.buffer.byteLength) {\n      throw new RangeError(\"byteOffset out of range\");\n    }\n\n    if (arguments.length < 3) {\n      this.byteLength = this.buffer.byteLength - this.byteOffset;\n    } else {\n      this.byteLength = ECMAScript.ToUint32(byteLength);\n    }\n\n    if ((this.byteOffset + this.byteLength) > this.buffer.byteLength) {\n      throw new RangeError(\"byteOffset and length reference an area beyond the end of the buffer\");\n    }\n\n    configureProperties(this);\n  };\n\n  function makeGetter(arrayType) {\n    return function(byteOffset, littleEndian) {\n\n      byteOffset = ECMAScript.ToUint32(byteOffset);\n\n      if (byteOffset + arrayType.BYTES_PER_ELEMENT > this.byteLength) {\n        throw new RangeError(\"Array index out of range\");\n      }\n      byteOffset += this.byteOffset;\n\n      var uint8Array = new exports.Uint8Array(this.buffer, byteOffset, arrayType.BYTES_PER_ELEMENT),\n          bytes = [], i;\n      for (i = 0; i < arrayType.BYTES_PER_ELEMENT; i += 1) {\n        bytes.push(r(uint8Array, i));\n      }\n\n      if (Boolean(littleEndian) === Boolean(IS_BIG_ENDIAN)) {\n        bytes.reverse();\n      }\n\n      return r(new arrayType(new exports.Uint8Array(bytes).buffer), 0);\n    };\n  }\n\n  DataView.prototype.getUint8 = makeGetter(exports.Uint8Array);\n  DataView.prototype.getInt8 = makeGetter(exports.Int8Array);\n  DataView.prototype.getUint16 = makeGetter(exports.Uint16Array);\n  DataView.prototype.getInt16 = makeGetter(exports.Int16Array);\n  DataView.prototype.getUint32 = makeGetter(exports.Uint32Array);\n  DataView.prototype.getInt32 = makeGetter(exports.Int32Array);\n  DataView.prototype.getFloat32 = makeGetter(exports.Float32Array);\n  DataView.prototype.getFloat64 = makeGetter(exports.Float64Array);\n\n  function makeSetter(arrayType) {\n    return function(byteOffset, value, littleEndian) {\n\n      byteOffset = ECMAScript.ToUint32(byteOffset);\n      if (byteOffset + arrayType.BYTES_PER_ELEMENT > this.byteLength) {\n        throw new RangeError(\"Array index out of range\");\n      }\n\n      // Get bytes\n      var typeArray = new arrayType([value]),\n          byteArray = new exports.Uint8Array(typeArray.buffer),\n          bytes = [], i, byteView;\n\n      for (i = 0; i < arrayType.BYTES_PER_ELEMENT; i += 1) {\n        bytes.push(r(byteArray, i));\n      }\n\n      // Flip if necessary\n      if (Boolean(littleEndian) === Boolean(IS_BIG_ENDIAN)) {\n        bytes.reverse();\n      }\n\n      // Write them\n      byteView = new exports.Uint8Array(this.buffer, byteOffset, arrayType.BYTES_PER_ELEMENT);\n      byteView.set(bytes);\n    };\n  }\n\n  DataView.prototype.setUint8 = makeSetter(exports.Uint8Array);\n  DataView.prototype.setInt8 = makeSetter(exports.Int8Array);\n  DataView.prototype.setUint16 = makeSetter(exports.Uint16Array);\n  DataView.prototype.setInt16 = makeSetter(exports.Int16Array);\n  DataView.prototype.setUint32 = makeSetter(exports.Uint32Array);\n  DataView.prototype.setInt32 = makeSetter(exports.Int32Array);\n  DataView.prototype.setFloat32 = makeSetter(exports.Float32Array);\n  DataView.prototype.setFloat64 = makeSetter(exports.Float64Array);\n\n  exports.DataView = exports.DataView || DataView;\n\n}());\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvdHlwZWRhcnJheS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBQSwwQkFBMEI7O0FBRTFCO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSx5QkFBeUIsc0RBQXNEO0FBQy9FLGtDQUFrQyxnQkFBZ0I7QUFDbEQscUNBQXFDLDBCQUEwQjtBQUMvRCw4QkFBOEIsaUNBQWlDO0FBQy9ELDJCQUEyQixnQkFBZ0I7QUFDM0MsNEJBQTRCO0FBQzVCO0FBQ0EsQ0FBQzs7QUFFRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLGtCQUFrQjtBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQyxTQUFTO0FBQ3pDO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSxFQUFFO0FBQ0Y7QUFDQTtBQUNBLG9GQUFvRjtBQUNwRixvRkFBb0Y7QUFDcEYsaURBQWlEO0FBQ2pEO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7O0FBRXJCOztBQUVBO0FBQ0E7QUFDQSwwQkFBMEIsNEJBQTRCO0FBQ3RELDJCQUEyQix3QkFBd0I7QUFDbkQ7QUFDQTtBQUNBLEtBQUs7QUFDTDs7QUFFQTtBQUNBLGNBQWMsZ0JBQWdCO0FBQzlCO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUEsa0NBQWtDLG1CQUFtQjtBQUNyRCxvQ0FBb0MsbUJBQW1COztBQUV2RCxxQkFBcUI7QUFDckIsMkJBQTJCOztBQUUzQixxQkFBcUI7QUFDckIsMkJBQTJCOztBQUUzQiw0QkFBNEIsc0JBQXNCOztBQUVsRCxzQkFBc0I7QUFDdEIsNEJBQTRCOztBQUU1QixzQkFBc0I7QUFDdEIsNEJBQTRCOztBQUU1QixzQkFBc0I7QUFDdEIsNEJBQTRCOztBQUU1QixzQkFBc0I7QUFDdEIsNEJBQTRCOztBQUU1Qjs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQix1QkFBdUI7QUFDakQsSUFBSTtBQUNKLDBCQUEwQixPQUFPO0FBQ2pDLElBQUk7QUFDSixXQUFXLE9BQU87QUFDbEIsSUFBSTtBQUNKO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxrQkFBa0IsR0FBRyxVQUFVLDBCQUEwQjtBQUN6RCxrQkFBa0IsR0FBRyxVQUFVLDBCQUEwQjtBQUN6RDtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUEseUJBQXlCLEdBQUc7QUFDNUI7QUFDQSxnQkFBZ0IsR0FBRztBQUNuQixnQ0FBZ0M7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTs7QUFFQSx3QkFBd0I7QUFDeEIsc0JBQXNCO0FBQ3RCLHdCQUF3QjtBQUN4QixzQkFBc0I7OztBQUd0QjtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0EsZ0JBQWdCLHFCQUFxQjtBQUNyQztBQUNBOztBQUVBO0FBQ0E7O0FBRUEsRUFBRSxtQkFBbUI7O0FBRXJCO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxvQkFBb0IsaUJBQWlCO0FBQ3JDO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLG9CQUFvQixpQkFBaUI7QUFDckM7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLDRDQUE0QyxnQkFBZ0I7QUFDNUQ7QUFDQTtBQUNBLHNDQUFzQyxnQkFBZ0I7QUFDdEQ7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBLCtCQUErQjtBQUMvQjtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBLG9CQUFvQixTQUFTO0FBQzdCO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLG9DQUFvQzs7QUFFcEM7QUFDQTs7QUFFQSxrQ0FBa0M7QUFDbEMsa0NBQWtDOztBQUVsQyx1QkFBdUI7QUFDdkIscUJBQXFCOztBQUVyQjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxFQUFFLGlCQUFpQjtBQUNuQixFQUFFLGtCQUFrQjtBQUNwQixFQUFFLHlCQUF5QjtBQUMzQixFQUFFLGtCQUFrQjtBQUNwQixFQUFFLG1CQUFtQjtBQUNyQixFQUFFLGtCQUFrQjtBQUNwQixFQUFFLG1CQUFtQjtBQUNyQixFQUFFLG9CQUFvQjtBQUN0QixFQUFFLG9CQUFvQjtBQUN0QixDQUFDOztBQUVEO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0Esa0JBQWtCLGlDQUFpQztBQUNuRDtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLGtCQUFrQixpQ0FBaUM7QUFDbkQ7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsRUFBRSxnQkFBZ0I7O0FBRWxCLENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zcnNyLXByb3BlcnR5LW1hbmFnZW1lbnQtYmFja2VuZC8uL25vZGVfbW9kdWxlcy90eXBlZGFycmF5L2luZGV4LmpzPzQyNDciXSwic291cmNlc0NvbnRlbnQiOlsidmFyIHVuZGVmaW5lZCA9ICh2b2lkIDApOyAvLyBQYXJhbm9pYVxuXG4vLyBCZXlvbmQgdGhpcyB2YWx1ZSwgaW5kZXggZ2V0dGVycy9zZXR0ZXJzIChpLmUuIGFycmF5WzBdLCBhcnJheVsxXSkgYXJlIHNvIHNsb3cgdG9cbi8vIGNyZWF0ZSwgYW5kIGNvbnN1bWUgc28gbXVjaCBtZW1vcnksIHRoYXQgdGhlIGJyb3dzZXIgYXBwZWFycyBmcm96ZW4uXG52YXIgTUFYX0FSUkFZX0xFTkdUSCA9IDFlNTtcblxuLy8gQXBwcm94aW1hdGlvbnMgb2YgaW50ZXJuYWwgRUNNQVNjcmlwdCBjb252ZXJzaW9uIGZ1bmN0aW9uc1xudmFyIEVDTUFTY3JpcHQgPSAoZnVuY3Rpb24oKSB7XG4gIC8vIFN0YXNoIGEgY29weSBpbiBjYXNlIG90aGVyIHNjcmlwdHMgbW9kaWZ5IHRoZXNlXG4gIHZhciBvcHRzID0gT2JqZWN0LnByb3RvdHlwZS50b1N0cmluZyxcbiAgICAgIG9waG9wID0gT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eTtcblxuICByZXR1cm4ge1xuICAgIC8vIENsYXNzIHJldHVybnMgaW50ZXJuYWwgW1tDbGFzc11dIHByb3BlcnR5LCB1c2VkIHRvIGF2b2lkIGNyb3NzLWZyYW1lIGluc3RhbmNlb2YgaXNzdWVzOlxuICAgIENsYXNzOiBmdW5jdGlvbih2KSB7IHJldHVybiBvcHRzLmNhbGwodikucmVwbGFjZSgvXlxcW29iamVjdCAqfFxcXSQvZywgJycpOyB9LFxuICAgIEhhc1Byb3BlcnR5OiBmdW5jdGlvbihvLCBwKSB7IHJldHVybiBwIGluIG87IH0sXG4gICAgSGFzT3duUHJvcGVydHk6IGZ1bmN0aW9uKG8sIHApIHsgcmV0dXJuIG9waG9wLmNhbGwobywgcCk7IH0sXG4gICAgSXNDYWxsYWJsZTogZnVuY3Rpb24obykgeyByZXR1cm4gdHlwZW9mIG8gPT09ICdmdW5jdGlvbic7IH0sXG4gICAgVG9JbnQzMjogZnVuY3Rpb24odikgeyByZXR1cm4gdiA+PiAwOyB9LFxuICAgIFRvVWludDMyOiBmdW5jdGlvbih2KSB7IHJldHVybiB2ID4+PiAwOyB9XG4gIH07XG59KCkpO1xuXG4vLyBTbmFwc2hvdCBpbnRyaW5zaWNzXG52YXIgTE4yID0gTWF0aC5MTjIsXG4gICAgYWJzID0gTWF0aC5hYnMsXG4gICAgZmxvb3IgPSBNYXRoLmZsb29yLFxuICAgIGxvZyA9IE1hdGgubG9nLFxuICAgIG1pbiA9IE1hdGgubWluLFxuICAgIHBvdyA9IE1hdGgucG93LFxuICAgIHJvdW5kID0gTWF0aC5yb3VuZDtcblxuLy8gRVM1OiBsb2NrIGRvd24gb2JqZWN0IHByb3BlcnRpZXNcbmZ1bmN0aW9uIGNvbmZpZ3VyZVByb3BlcnRpZXMob2JqKSB7XG4gIGlmIChnZXRPd25Qcm9wTmFtZXMgJiYgZGVmaW5lUHJvcCkge1xuICAgIHZhciBwcm9wcyA9IGdldE93blByb3BOYW1lcyhvYmopLCBpO1xuICAgIGZvciAoaSA9IDA7IGkgPCBwcm9wcy5sZW5ndGg7IGkgKz0gMSkge1xuICAgICAgZGVmaW5lUHJvcChvYmosIHByb3BzW2ldLCB7XG4gICAgICAgIHZhbHVlOiBvYmpbcHJvcHNbaV1dLFxuICAgICAgICB3cml0YWJsZTogZmFsc2UsXG4gICAgICAgIGVudW1lcmFibGU6IGZhbHNlLFxuICAgICAgICBjb25maWd1cmFibGU6IGZhbHNlXG4gICAgICB9KTtcbiAgICB9XG4gIH1cbn1cblxuLy8gZW11bGF0ZSBFUzUgZ2V0dGVyL3NldHRlciBBUEkgdXNpbmcgbGVnYWN5IEFQSXNcbi8vIGh0dHA6Ly9ibG9ncy5tc2RuLmNvbS9iL2llL2FyY2hpdmUvMjAxMC8wOS8wNy90cmFuc2l0aW9uaW5nLWV4aXN0aW5nLWNvZGUtdG8tdGhlLWVzNS1nZXR0ZXItc2V0dGVyLWFwaXMuYXNweFxuLy8gKHNlY29uZCBjbGF1c2UgdGVzdHMgZm9yIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSgpIGluIElFPDkgdGhhdCBvbmx5IHN1cHBvcnRzIGV4dGVuZGluZyBET00gcHJvdG90eXBlcywgYnV0XG4vLyBub3RlIHRoYXQgSUU8OSBkb2VzIG5vdCBzdXBwb3J0IF9fZGVmaW5lR2V0dGVyX18gb3IgX19kZWZpbmVTZXR0ZXJfXyBzbyBpdCBqdXN0IHJlbmRlcnMgdGhlIG1ldGhvZCBoYXJtbGVzcylcbnZhciBkZWZpbmVQcm9wXG5pZiAoT2JqZWN0LmRlZmluZVByb3BlcnR5ICYmIChmdW5jdGlvbigpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh7fSwgJ3gnLCB7fSk7XG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgfSBjYXRjaCAoZSkge1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICB9XG4gICAgfSkoKSkge1xuICBkZWZpbmVQcm9wID0gT2JqZWN0LmRlZmluZVByb3BlcnR5O1xufSBlbHNlIHtcbiAgZGVmaW5lUHJvcCA9IGZ1bmN0aW9uKG8sIHAsIGRlc2MpIHtcbiAgICBpZiAoIW8gPT09IE9iamVjdChvKSkgdGhyb3cgbmV3IFR5cGVFcnJvcihcIk9iamVjdC5kZWZpbmVQcm9wZXJ0eSBjYWxsZWQgb24gbm9uLW9iamVjdFwiKTtcbiAgICBpZiAoRUNNQVNjcmlwdC5IYXNQcm9wZXJ0eShkZXNjLCAnZ2V0JykgJiYgT2JqZWN0LnByb3RvdHlwZS5fX2RlZmluZUdldHRlcl9fKSB7IE9iamVjdC5wcm90b3R5cGUuX19kZWZpbmVHZXR0ZXJfXy5jYWxsKG8sIHAsIGRlc2MuZ2V0KTsgfVxuICAgIGlmIChFQ01BU2NyaXB0Lkhhc1Byb3BlcnR5KGRlc2MsICdzZXQnKSAmJiBPYmplY3QucHJvdG90eXBlLl9fZGVmaW5lU2V0dGVyX18pIHsgT2JqZWN0LnByb3RvdHlwZS5fX2RlZmluZVNldHRlcl9fLmNhbGwobywgcCwgZGVzYy5zZXQpOyB9XG4gICAgaWYgKEVDTUFTY3JpcHQuSGFzUHJvcGVydHkoZGVzYywgJ3ZhbHVlJykpIHsgb1twXSA9IGRlc2MudmFsdWU7IH1cbiAgICByZXR1cm4gbztcbiAgfTtcbn1cblxudmFyIGdldE93blByb3BOYW1lcyA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eU5hbWVzIHx8IGZ1bmN0aW9uIChvKSB7XG4gIGlmIChvICE9PSBPYmplY3QobykpIHRocm93IG5ldyBUeXBlRXJyb3IoXCJPYmplY3QuZ2V0T3duUHJvcGVydHlOYW1lcyBjYWxsZWQgb24gbm9uLW9iamVjdFwiKTtcbiAgdmFyIHByb3BzID0gW10sIHA7XG4gIGZvciAocCBpbiBvKSB7XG4gICAgaWYgKEVDTUFTY3JpcHQuSGFzT3duUHJvcGVydHkobywgcCkpIHtcbiAgICAgIHByb3BzLnB1c2gocCk7XG4gICAgfVxuICB9XG4gIHJldHVybiBwcm9wcztcbn07XG5cbi8vIEVTNTogTWFrZSBvYmpbaW5kZXhdIGFuIGFsaWFzIGZvciBvYmouX2dldHRlcihpbmRleCkvb2JqLl9zZXR0ZXIoaW5kZXgsIHZhbHVlKVxuLy8gZm9yIGluZGV4IGluIDAgLi4uIG9iai5sZW5ndGhcbmZ1bmN0aW9uIG1ha2VBcnJheUFjY2Vzc29ycyhvYmopIHtcbiAgaWYgKCFkZWZpbmVQcm9wKSB7IHJldHVybjsgfVxuXG4gIGlmIChvYmoubGVuZ3RoID4gTUFYX0FSUkFZX0xFTkdUSCkgdGhyb3cgbmV3IFJhbmdlRXJyb3IoXCJBcnJheSB0b28gbGFyZ2UgZm9yIHBvbHlmaWxsXCIpO1xuXG4gIGZ1bmN0aW9uIG1ha2VBcnJheUFjY2Vzc29yKGluZGV4KSB7XG4gICAgZGVmaW5lUHJvcChvYmosIGluZGV4LCB7XG4gICAgICAnZ2V0JzogZnVuY3Rpb24oKSB7IHJldHVybiBvYmouX2dldHRlcihpbmRleCk7IH0sXG4gICAgICAnc2V0JzogZnVuY3Rpb24odikgeyBvYmouX3NldHRlcihpbmRleCwgdik7IH0sXG4gICAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgICAgY29uZmlndXJhYmxlOiBmYWxzZVxuICAgIH0pO1xuICB9XG5cbiAgdmFyIGk7XG4gIGZvciAoaSA9IDA7IGkgPCBvYmoubGVuZ3RoOyBpICs9IDEpIHtcbiAgICBtYWtlQXJyYXlBY2Nlc3NvcihpKTtcbiAgfVxufVxuXG4vLyBJbnRlcm5hbCBjb252ZXJzaW9uIGZ1bmN0aW9uczpcbi8vICAgIHBhY2s8VHlwZT4oKSAgIC0gdGFrZSBhIG51bWJlciAoaW50ZXJwcmV0ZWQgYXMgVHlwZSksIG91dHB1dCBhIGJ5dGUgYXJyYXlcbi8vICAgIHVucGFjazxUeXBlPigpIC0gdGFrZSBhIGJ5dGUgYXJyYXksIG91dHB1dCBhIFR5cGUtbGlrZSBudW1iZXJcblxuZnVuY3Rpb24gYXNfc2lnbmVkKHZhbHVlLCBiaXRzKSB7IHZhciBzID0gMzIgLSBiaXRzOyByZXR1cm4gKHZhbHVlIDw8IHMpID4+IHM7IH1cbmZ1bmN0aW9uIGFzX3Vuc2lnbmVkKHZhbHVlLCBiaXRzKSB7IHZhciBzID0gMzIgLSBiaXRzOyByZXR1cm4gKHZhbHVlIDw8IHMpID4+PiBzOyB9XG5cbmZ1bmN0aW9uIHBhY2tJOChuKSB7IHJldHVybiBbbiAmIDB4ZmZdOyB9XG5mdW5jdGlvbiB1bnBhY2tJOChieXRlcykgeyByZXR1cm4gYXNfc2lnbmVkKGJ5dGVzWzBdLCA4KTsgfVxuXG5mdW5jdGlvbiBwYWNrVTgobikgeyByZXR1cm4gW24gJiAweGZmXTsgfVxuZnVuY3Rpb24gdW5wYWNrVTgoYnl0ZXMpIHsgcmV0dXJuIGFzX3Vuc2lnbmVkKGJ5dGVzWzBdLCA4KTsgfVxuXG5mdW5jdGlvbiBwYWNrVThDbGFtcGVkKG4pIHsgbiA9IHJvdW5kKE51bWJlcihuKSk7IHJldHVybiBbbiA8IDAgPyAwIDogbiA+IDB4ZmYgPyAweGZmIDogbiAmIDB4ZmZdOyB9XG5cbmZ1bmN0aW9uIHBhY2tJMTYobikgeyByZXR1cm4gWyhuID4+IDgpICYgMHhmZiwgbiAmIDB4ZmZdOyB9XG5mdW5jdGlvbiB1bnBhY2tJMTYoYnl0ZXMpIHsgcmV0dXJuIGFzX3NpZ25lZChieXRlc1swXSA8PCA4IHwgYnl0ZXNbMV0sIDE2KTsgfVxuXG5mdW5jdGlvbiBwYWNrVTE2KG4pIHsgcmV0dXJuIFsobiA+PiA4KSAmIDB4ZmYsIG4gJiAweGZmXTsgfVxuZnVuY3Rpb24gdW5wYWNrVTE2KGJ5dGVzKSB7IHJldHVybiBhc191bnNpZ25lZChieXRlc1swXSA8PCA4IHwgYnl0ZXNbMV0sIDE2KTsgfVxuXG5mdW5jdGlvbiBwYWNrSTMyKG4pIHsgcmV0dXJuIFsobiA+PiAyNCkgJiAweGZmLCAobiA+PiAxNikgJiAweGZmLCAobiA+PiA4KSAmIDB4ZmYsIG4gJiAweGZmXTsgfVxuZnVuY3Rpb24gdW5wYWNrSTMyKGJ5dGVzKSB7IHJldHVybiBhc19zaWduZWQoYnl0ZXNbMF0gPDwgMjQgfCBieXRlc1sxXSA8PCAxNiB8IGJ5dGVzWzJdIDw8IDggfCBieXRlc1szXSwgMzIpOyB9XG5cbmZ1bmN0aW9uIHBhY2tVMzIobikgeyByZXR1cm4gWyhuID4+IDI0KSAmIDB4ZmYsIChuID4+IDE2KSAmIDB4ZmYsIChuID4+IDgpICYgMHhmZiwgbiAmIDB4ZmZdOyB9XG5mdW5jdGlvbiB1bnBhY2tVMzIoYnl0ZXMpIHsgcmV0dXJuIGFzX3Vuc2lnbmVkKGJ5dGVzWzBdIDw8IDI0IHwgYnl0ZXNbMV0gPDwgMTYgfCBieXRlc1syXSA8PCA4IHwgYnl0ZXNbM10sIDMyKTsgfVxuXG5mdW5jdGlvbiBwYWNrSUVFRTc1NCh2LCBlYml0cywgZmJpdHMpIHtcblxuICB2YXIgYmlhcyA9ICgxIDw8IChlYml0cyAtIDEpKSAtIDEsXG4gICAgICBzLCBlLCBmLCBsbixcbiAgICAgIGksIGJpdHMsIHN0ciwgYnl0ZXM7XG5cbiAgZnVuY3Rpb24gcm91bmRUb0V2ZW4obikge1xuICAgIHZhciB3ID0gZmxvb3IobiksIGYgPSBuIC0gdztcbiAgICBpZiAoZiA8IDAuNSlcbiAgICAgIHJldHVybiB3O1xuICAgIGlmIChmID4gMC41KVxuICAgICAgcmV0dXJuIHcgKyAxO1xuICAgIHJldHVybiB3ICUgMiA/IHcgKyAxIDogdztcbiAgfVxuXG4gIC8vIENvbXB1dGUgc2lnbiwgZXhwb25lbnQsIGZyYWN0aW9uXG4gIGlmICh2ICE9PSB2KSB7XG4gICAgLy8gTmFOXG4gICAgLy8gaHR0cDovL2Rldi53My5vcmcvMjAwNi93ZWJhcGkvV2ViSURMLyNlcy10eXBlLW1hcHBpbmdcbiAgICBlID0gKDEgPDwgZWJpdHMpIC0gMTsgZiA9IHBvdygyLCBmYml0cyAtIDEpOyBzID0gMDtcbiAgfSBlbHNlIGlmICh2ID09PSBJbmZpbml0eSB8fCB2ID09PSAtSW5maW5pdHkpIHtcbiAgICBlID0gKDEgPDwgZWJpdHMpIC0gMTsgZiA9IDA7IHMgPSAodiA8IDApID8gMSA6IDA7XG4gIH0gZWxzZSBpZiAodiA9PT0gMCkge1xuICAgIGUgPSAwOyBmID0gMDsgcyA9ICgxIC8gdiA9PT0gLUluZmluaXR5KSA/IDEgOiAwO1xuICB9IGVsc2Uge1xuICAgIHMgPSB2IDwgMDtcbiAgICB2ID0gYWJzKHYpO1xuXG4gICAgaWYgKHYgPj0gcG93KDIsIDEgLSBiaWFzKSkge1xuICAgICAgZSA9IG1pbihmbG9vcihsb2codikgLyBMTjIpLCAxMDIzKTtcbiAgICAgIGYgPSByb3VuZFRvRXZlbih2IC8gcG93KDIsIGUpICogcG93KDIsIGZiaXRzKSk7XG4gICAgICBpZiAoZiAvIHBvdygyLCBmYml0cykgPj0gMikge1xuICAgICAgICBlID0gZSArIDE7XG4gICAgICAgIGYgPSAxO1xuICAgICAgfVxuICAgICAgaWYgKGUgPiBiaWFzKSB7XG4gICAgICAgIC8vIE92ZXJmbG93XG4gICAgICAgIGUgPSAoMSA8PCBlYml0cykgLSAxO1xuICAgICAgICBmID0gMDtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIC8vIE5vcm1hbGl6ZWRcbiAgICAgICAgZSA9IGUgKyBiaWFzO1xuICAgICAgICBmID0gZiAtIHBvdygyLCBmYml0cyk7XG4gICAgICB9XG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIERlbm9ybWFsaXplZFxuICAgICAgZSA9IDA7XG4gICAgICBmID0gcm91bmRUb0V2ZW4odiAvIHBvdygyLCAxIC0gYmlhcyAtIGZiaXRzKSk7XG4gICAgfVxuICB9XG5cbiAgLy8gUGFjayBzaWduLCBleHBvbmVudCwgZnJhY3Rpb25cbiAgYml0cyA9IFtdO1xuICBmb3IgKGkgPSBmYml0czsgaTsgaSAtPSAxKSB7IGJpdHMucHVzaChmICUgMiA/IDEgOiAwKTsgZiA9IGZsb29yKGYgLyAyKTsgfVxuICBmb3IgKGkgPSBlYml0czsgaTsgaSAtPSAxKSB7IGJpdHMucHVzaChlICUgMiA/IDEgOiAwKTsgZSA9IGZsb29yKGUgLyAyKTsgfVxuICBiaXRzLnB1c2gocyA/IDEgOiAwKTtcbiAgYml0cy5yZXZlcnNlKCk7XG4gIHN0ciA9IGJpdHMuam9pbignJyk7XG5cbiAgLy8gQml0cyB0byBieXRlc1xuICBieXRlcyA9IFtdO1xuICB3aGlsZSAoc3RyLmxlbmd0aCkge1xuICAgIGJ5dGVzLnB1c2gocGFyc2VJbnQoc3RyLnN1YnN0cmluZygwLCA4KSwgMikpO1xuICAgIHN0ciA9IHN0ci5zdWJzdHJpbmcoOCk7XG4gIH1cbiAgcmV0dXJuIGJ5dGVzO1xufVxuXG5mdW5jdGlvbiB1bnBhY2tJRUVFNzU0KGJ5dGVzLCBlYml0cywgZmJpdHMpIHtcblxuICAvLyBCeXRlcyB0byBiaXRzXG4gIHZhciBiaXRzID0gW10sIGksIGosIGIsIHN0cixcbiAgICAgIGJpYXMsIHMsIGUsIGY7XG5cbiAgZm9yIChpID0gYnl0ZXMubGVuZ3RoOyBpOyBpIC09IDEpIHtcbiAgICBiID0gYnl0ZXNbaSAtIDFdO1xuICAgIGZvciAoaiA9IDg7IGo7IGogLT0gMSkge1xuICAgICAgYml0cy5wdXNoKGIgJSAyID8gMSA6IDApOyBiID0gYiA+PiAxO1xuICAgIH1cbiAgfVxuICBiaXRzLnJldmVyc2UoKTtcbiAgc3RyID0gYml0cy5qb2luKCcnKTtcblxuICAvLyBVbnBhY2sgc2lnbiwgZXhwb25lbnQsIGZyYWN0aW9uXG4gIGJpYXMgPSAoMSA8PCAoZWJpdHMgLSAxKSkgLSAxO1xuICBzID0gcGFyc2VJbnQoc3RyLnN1YnN0cmluZygwLCAxKSwgMikgPyAtMSA6IDE7XG4gIGUgPSBwYXJzZUludChzdHIuc3Vic3RyaW5nKDEsIDEgKyBlYml0cyksIDIpO1xuICBmID0gcGFyc2VJbnQoc3RyLnN1YnN0cmluZygxICsgZWJpdHMpLCAyKTtcblxuICAvLyBQcm9kdWNlIG51bWJlclxuICBpZiAoZSA9PT0gKDEgPDwgZWJpdHMpIC0gMSkge1xuICAgIHJldHVybiBmICE9PSAwID8gTmFOIDogcyAqIEluZmluaXR5O1xuICB9IGVsc2UgaWYgKGUgPiAwKSB7XG4gICAgLy8gTm9ybWFsaXplZFxuICAgIHJldHVybiBzICogcG93KDIsIGUgLSBiaWFzKSAqICgxICsgZiAvIHBvdygyLCBmYml0cykpO1xuICB9IGVsc2UgaWYgKGYgIT09IDApIHtcbiAgICAvLyBEZW5vcm1hbGl6ZWRcbiAgICByZXR1cm4gcyAqIHBvdygyLCAtKGJpYXMgLSAxKSkgKiAoZiAvIHBvdygyLCBmYml0cykpO1xuICB9IGVsc2Uge1xuICAgIHJldHVybiBzIDwgMCA/IC0wIDogMDtcbiAgfVxufVxuXG5mdW5jdGlvbiB1bnBhY2tGNjQoYikgeyByZXR1cm4gdW5wYWNrSUVFRTc1NChiLCAxMSwgNTIpOyB9XG5mdW5jdGlvbiBwYWNrRjY0KHYpIHsgcmV0dXJuIHBhY2tJRUVFNzU0KHYsIDExLCA1Mik7IH1cbmZ1bmN0aW9uIHVucGFja0YzMihiKSB7IHJldHVybiB1bnBhY2tJRUVFNzU0KGIsIDgsIDIzKTsgfVxuZnVuY3Rpb24gcGFja0YzMih2KSB7IHJldHVybiBwYWNrSUVFRTc1NCh2LCA4LCAyMyk7IH1cblxuXG4vL1xuLy8gMyBUaGUgQXJyYXlCdWZmZXIgVHlwZVxuLy9cblxuKGZ1bmN0aW9uKCkge1xuXG4gIC8qKiBAY29uc3RydWN0b3IgKi9cbiAgdmFyIEFycmF5QnVmZmVyID0gZnVuY3Rpb24gQXJyYXlCdWZmZXIobGVuZ3RoKSB7XG4gICAgbGVuZ3RoID0gRUNNQVNjcmlwdC5Ub0ludDMyKGxlbmd0aCk7XG4gICAgaWYgKGxlbmd0aCA8IDApIHRocm93IG5ldyBSYW5nZUVycm9yKCdBcnJheUJ1ZmZlciBzaXplIGlzIG5vdCBhIHNtYWxsIGVub3VnaCBwb3NpdGl2ZSBpbnRlZ2VyJyk7XG5cbiAgICB0aGlzLmJ5dGVMZW5ndGggPSBsZW5ndGg7XG4gICAgdGhpcy5fYnl0ZXMgPSBbXTtcbiAgICB0aGlzLl9ieXRlcy5sZW5ndGggPSBsZW5ndGg7XG5cbiAgICB2YXIgaTtcbiAgICBmb3IgKGkgPSAwOyBpIDwgdGhpcy5ieXRlTGVuZ3RoOyBpICs9IDEpIHtcbiAgICAgIHRoaXMuX2J5dGVzW2ldID0gMDtcbiAgICB9XG5cbiAgICBjb25maWd1cmVQcm9wZXJ0aWVzKHRoaXMpO1xuICB9O1xuXG4gIGV4cG9ydHMuQXJyYXlCdWZmZXIgPSBleHBvcnRzLkFycmF5QnVmZmVyIHx8IEFycmF5QnVmZmVyO1xuXG4gIC8vXG4gIC8vIDQgVGhlIEFycmF5QnVmZmVyVmlldyBUeXBlXG4gIC8vXG5cbiAgLy8gTk9URTogdGhpcyBjb25zdHJ1Y3RvciBpcyBub3QgZXhwb3J0ZWRcbiAgLyoqIEBjb25zdHJ1Y3RvciAqL1xuICB2YXIgQXJyYXlCdWZmZXJWaWV3ID0gZnVuY3Rpb24gQXJyYXlCdWZmZXJWaWV3KCkge1xuICAgIC8vdGhpcy5idWZmZXIgPSBudWxsO1xuICAgIC8vdGhpcy5ieXRlT2Zmc2V0ID0gMDtcbiAgICAvL3RoaXMuYnl0ZUxlbmd0aCA9IDA7XG4gIH07XG5cbiAgLy9cbiAgLy8gNSBUaGUgVHlwZWQgQXJyYXkgVmlldyBUeXBlc1xuICAvL1xuXG4gIGZ1bmN0aW9uIG1ha2VDb25zdHJ1Y3RvcihieXRlc1BlckVsZW1lbnQsIHBhY2ssIHVucGFjaykge1xuICAgIC8vIEVhY2ggVHlwZWRBcnJheSB0eXBlIHJlcXVpcmVzIGEgZGlzdGluY3QgY29uc3RydWN0b3IgaW5zdGFuY2Ugd2l0aFxuICAgIC8vIGlkZW50aWNhbCBsb2dpYywgd2hpY2ggdGhpcyBwcm9kdWNlcy5cblxuICAgIHZhciBjdG9yO1xuICAgIGN0b3IgPSBmdW5jdGlvbihidWZmZXIsIGJ5dGVPZmZzZXQsIGxlbmd0aCkge1xuICAgICAgdmFyIGFycmF5LCBzZXF1ZW5jZSwgaSwgcztcblxuICAgICAgaWYgKCFhcmd1bWVudHMubGVuZ3RoIHx8IHR5cGVvZiBhcmd1bWVudHNbMF0gPT09ICdudW1iZXInKSB7XG4gICAgICAgIC8vIENvbnN0cnVjdG9yKHVuc2lnbmVkIGxvbmcgbGVuZ3RoKVxuICAgICAgICB0aGlzLmxlbmd0aCA9IEVDTUFTY3JpcHQuVG9JbnQzMihhcmd1bWVudHNbMF0pO1xuICAgICAgICBpZiAobGVuZ3RoIDwgMCkgdGhyb3cgbmV3IFJhbmdlRXJyb3IoJ0FycmF5QnVmZmVyVmlldyBzaXplIGlzIG5vdCBhIHNtYWxsIGVub3VnaCBwb3NpdGl2ZSBpbnRlZ2VyJyk7XG5cbiAgICAgICAgdGhpcy5ieXRlTGVuZ3RoID0gdGhpcy5sZW5ndGggKiB0aGlzLkJZVEVTX1BFUl9FTEVNRU5UO1xuICAgICAgICB0aGlzLmJ1ZmZlciA9IG5ldyBBcnJheUJ1ZmZlcih0aGlzLmJ5dGVMZW5ndGgpO1xuICAgICAgICB0aGlzLmJ5dGVPZmZzZXQgPSAwO1xuICAgICAgfSBlbHNlIGlmICh0eXBlb2YgYXJndW1lbnRzWzBdID09PSAnb2JqZWN0JyAmJiBhcmd1bWVudHNbMF0uY29uc3RydWN0b3IgPT09IGN0b3IpIHtcbiAgICAgICAgLy8gQ29uc3RydWN0b3IoVHlwZWRBcnJheSBhcnJheSlcbiAgICAgICAgYXJyYXkgPSBhcmd1bWVudHNbMF07XG5cbiAgICAgICAgdGhpcy5sZW5ndGggPSBhcnJheS5sZW5ndGg7XG4gICAgICAgIHRoaXMuYnl0ZUxlbmd0aCA9IHRoaXMubGVuZ3RoICogdGhpcy5CWVRFU19QRVJfRUxFTUVOVDtcbiAgICAgICAgdGhpcy5idWZmZXIgPSBuZXcgQXJyYXlCdWZmZXIodGhpcy5ieXRlTGVuZ3RoKTtcbiAgICAgICAgdGhpcy5ieXRlT2Zmc2V0ID0gMDtcblxuICAgICAgICBmb3IgKGkgPSAwOyBpIDwgdGhpcy5sZW5ndGg7IGkgKz0gMSkge1xuICAgICAgICAgIHRoaXMuX3NldHRlcihpLCBhcnJheS5fZ2V0dGVyKGkpKTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIGlmICh0eXBlb2YgYXJndW1lbnRzWzBdID09PSAnb2JqZWN0JyAmJlxuICAgICAgICAgICAgICAgICAhKGFyZ3VtZW50c1swXSBpbnN0YW5jZW9mIEFycmF5QnVmZmVyIHx8IEVDTUFTY3JpcHQuQ2xhc3MoYXJndW1lbnRzWzBdKSA9PT0gJ0FycmF5QnVmZmVyJykpIHtcbiAgICAgICAgLy8gQ29uc3RydWN0b3Ioc2VxdWVuY2U8dHlwZT4gYXJyYXkpXG4gICAgICAgIHNlcXVlbmNlID0gYXJndW1lbnRzWzBdO1xuXG4gICAgICAgIHRoaXMubGVuZ3RoID0gRUNNQVNjcmlwdC5Ub1VpbnQzMihzZXF1ZW5jZS5sZW5ndGgpO1xuICAgICAgICB0aGlzLmJ5dGVMZW5ndGggPSB0aGlzLmxlbmd0aCAqIHRoaXMuQllURVNfUEVSX0VMRU1FTlQ7XG4gICAgICAgIHRoaXMuYnVmZmVyID0gbmV3IEFycmF5QnVmZmVyKHRoaXMuYnl0ZUxlbmd0aCk7XG4gICAgICAgIHRoaXMuYnl0ZU9mZnNldCA9IDA7XG5cbiAgICAgICAgZm9yIChpID0gMDsgaSA8IHRoaXMubGVuZ3RoOyBpICs9IDEpIHtcbiAgICAgICAgICBzID0gc2VxdWVuY2VbaV07XG4gICAgICAgICAgdGhpcy5fc2V0dGVyKGksIE51bWJlcihzKSk7XG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSBpZiAodHlwZW9mIGFyZ3VtZW50c1swXSA9PT0gJ29iamVjdCcgJiZcbiAgICAgICAgICAgICAgICAgKGFyZ3VtZW50c1swXSBpbnN0YW5jZW9mIEFycmF5QnVmZmVyIHx8IEVDTUFTY3JpcHQuQ2xhc3MoYXJndW1lbnRzWzBdKSA9PT0gJ0FycmF5QnVmZmVyJykpIHtcbiAgICAgICAgLy8gQ29uc3RydWN0b3IoQXJyYXlCdWZmZXIgYnVmZmVyLFxuICAgICAgICAvLyAgICAgICAgICAgICBvcHRpb25hbCB1bnNpZ25lZCBsb25nIGJ5dGVPZmZzZXQsIG9wdGlvbmFsIHVuc2lnbmVkIGxvbmcgbGVuZ3RoKVxuICAgICAgICB0aGlzLmJ1ZmZlciA9IGJ1ZmZlcjtcblxuICAgICAgICB0aGlzLmJ5dGVPZmZzZXQgPSBFQ01BU2NyaXB0LlRvVWludDMyKGJ5dGVPZmZzZXQpO1xuICAgICAgICBpZiAodGhpcy5ieXRlT2Zmc2V0ID4gdGhpcy5idWZmZXIuYnl0ZUxlbmd0aCkge1xuICAgICAgICAgIHRocm93IG5ldyBSYW5nZUVycm9yKFwiYnl0ZU9mZnNldCBvdXQgb2YgcmFuZ2VcIik7XG4gICAgICAgIH1cblxuICAgICAgICBpZiAodGhpcy5ieXRlT2Zmc2V0ICUgdGhpcy5CWVRFU19QRVJfRUxFTUVOVCkge1xuICAgICAgICAgIC8vIFRoZSBnaXZlbiBieXRlT2Zmc2V0IG11c3QgYmUgYSBtdWx0aXBsZSBvZiB0aGUgZWxlbWVudFxuICAgICAgICAgIC8vIHNpemUgb2YgdGhlIHNwZWNpZmljIHR5cGUsIG90aGVyd2lzZSBhbiBleGNlcHRpb24gaXMgcmFpc2VkLlxuICAgICAgICAgIHRocm93IG5ldyBSYW5nZUVycm9yKFwiQXJyYXlCdWZmZXIgbGVuZ3RoIG1pbnVzIHRoZSBieXRlT2Zmc2V0IGlzIG5vdCBhIG11bHRpcGxlIG9mIHRoZSBlbGVtZW50IHNpemUuXCIpO1xuICAgICAgICB9XG5cbiAgICAgICAgaWYgKGFyZ3VtZW50cy5sZW5ndGggPCAzKSB7XG4gICAgICAgICAgdGhpcy5ieXRlTGVuZ3RoID0gdGhpcy5idWZmZXIuYnl0ZUxlbmd0aCAtIHRoaXMuYnl0ZU9mZnNldDtcblxuICAgICAgICAgIGlmICh0aGlzLmJ5dGVMZW5ndGggJSB0aGlzLkJZVEVTX1BFUl9FTEVNRU5UKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgUmFuZ2VFcnJvcihcImxlbmd0aCBvZiBidWZmZXIgbWludXMgYnl0ZU9mZnNldCBub3QgYSBtdWx0aXBsZSBvZiB0aGUgZWxlbWVudCBzaXplXCIpO1xuICAgICAgICAgIH1cbiAgICAgICAgICB0aGlzLmxlbmd0aCA9IHRoaXMuYnl0ZUxlbmd0aCAvIHRoaXMuQllURVNfUEVSX0VMRU1FTlQ7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgdGhpcy5sZW5ndGggPSBFQ01BU2NyaXB0LlRvVWludDMyKGxlbmd0aCk7XG4gICAgICAgICAgdGhpcy5ieXRlTGVuZ3RoID0gdGhpcy5sZW5ndGggKiB0aGlzLkJZVEVTX1BFUl9FTEVNRU5UO1xuICAgICAgICB9XG5cbiAgICAgICAgaWYgKCh0aGlzLmJ5dGVPZmZzZXQgKyB0aGlzLmJ5dGVMZW5ndGgpID4gdGhpcy5idWZmZXIuYnl0ZUxlbmd0aCkge1xuICAgICAgICAgIHRocm93IG5ldyBSYW5nZUVycm9yKFwiYnl0ZU9mZnNldCBhbmQgbGVuZ3RoIHJlZmVyZW5jZSBhbiBhcmVhIGJleW9uZCB0aGUgZW5kIG9mIHRoZSBidWZmZXJcIik7XG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoXCJVbmV4cGVjdGVkIGFyZ3VtZW50IHR5cGUocylcIik7XG4gICAgICB9XG5cbiAgICAgIHRoaXMuY29uc3RydWN0b3IgPSBjdG9yO1xuXG4gICAgICBjb25maWd1cmVQcm9wZXJ0aWVzKHRoaXMpO1xuICAgICAgbWFrZUFycmF5QWNjZXNzb3JzKHRoaXMpO1xuICAgIH07XG5cbiAgICBjdG9yLnByb3RvdHlwZSA9IG5ldyBBcnJheUJ1ZmZlclZpZXcoKTtcbiAgICBjdG9yLnByb3RvdHlwZS5CWVRFU19QRVJfRUxFTUVOVCA9IGJ5dGVzUGVyRWxlbWVudDtcbiAgICBjdG9yLnByb3RvdHlwZS5fcGFjayA9IHBhY2s7XG4gICAgY3Rvci5wcm90b3R5cGUuX3VucGFjayA9IHVucGFjaztcbiAgICBjdG9yLkJZVEVTX1BFUl9FTEVNRU5UID0gYnl0ZXNQZXJFbGVtZW50O1xuXG4gICAgLy8gZ2V0dGVyIHR5cGUgKHVuc2lnbmVkIGxvbmcgaW5kZXgpO1xuICAgIGN0b3IucHJvdG90eXBlLl9nZXR0ZXIgPSBmdW5jdGlvbihpbmRleCkge1xuICAgICAgaWYgKGFyZ3VtZW50cy5sZW5ndGggPCAxKSB0aHJvdyBuZXcgU3ludGF4RXJyb3IoXCJOb3QgZW5vdWdoIGFyZ3VtZW50c1wiKTtcblxuICAgICAgaW5kZXggPSBFQ01BU2NyaXB0LlRvVWludDMyKGluZGV4KTtcbiAgICAgIGlmIChpbmRleCA+PSB0aGlzLmxlbmd0aCkge1xuICAgICAgICByZXR1cm4gdW5kZWZpbmVkO1xuICAgICAgfVxuXG4gICAgICB2YXIgYnl0ZXMgPSBbXSwgaSwgbztcbiAgICAgIGZvciAoaSA9IDAsIG8gPSB0aGlzLmJ5dGVPZmZzZXQgKyBpbmRleCAqIHRoaXMuQllURVNfUEVSX0VMRU1FTlQ7XG4gICAgICAgICAgIGkgPCB0aGlzLkJZVEVTX1BFUl9FTEVNRU5UO1xuICAgICAgICAgICBpICs9IDEsIG8gKz0gMSkge1xuICAgICAgICBieXRlcy5wdXNoKHRoaXMuYnVmZmVyLl9ieXRlc1tvXSk7XG4gICAgICB9XG4gICAgICByZXR1cm4gdGhpcy5fdW5wYWNrKGJ5dGVzKTtcbiAgICB9O1xuXG4gICAgLy8gTk9OU1RBTkRBUkQ6IGNvbnZlbmllbmNlIGFsaWFzIGZvciBnZXR0ZXI6IHR5cGUgZ2V0KHVuc2lnbmVkIGxvbmcgaW5kZXgpO1xuICAgIGN0b3IucHJvdG90eXBlLmdldCA9IGN0b3IucHJvdG90eXBlLl9nZXR0ZXI7XG5cbiAgICAvLyBzZXR0ZXIgdm9pZCAodW5zaWduZWQgbG9uZyBpbmRleCwgdHlwZSB2YWx1ZSk7XG4gICAgY3Rvci5wcm90b3R5cGUuX3NldHRlciA9IGZ1bmN0aW9uKGluZGV4LCB2YWx1ZSkge1xuICAgICAgaWYgKGFyZ3VtZW50cy5sZW5ndGggPCAyKSB0aHJvdyBuZXcgU3ludGF4RXJyb3IoXCJOb3QgZW5vdWdoIGFyZ3VtZW50c1wiKTtcblxuICAgICAgaW5kZXggPSBFQ01BU2NyaXB0LlRvVWludDMyKGluZGV4KTtcbiAgICAgIGlmIChpbmRleCA+PSB0aGlzLmxlbmd0aCkge1xuICAgICAgICByZXR1cm4gdW5kZWZpbmVkO1xuICAgICAgfVxuXG4gICAgICB2YXIgYnl0ZXMgPSB0aGlzLl9wYWNrKHZhbHVlKSwgaSwgbztcbiAgICAgIGZvciAoaSA9IDAsIG8gPSB0aGlzLmJ5dGVPZmZzZXQgKyBpbmRleCAqIHRoaXMuQllURVNfUEVSX0VMRU1FTlQ7XG4gICAgICAgICAgIGkgPCB0aGlzLkJZVEVTX1BFUl9FTEVNRU5UO1xuICAgICAgICAgICBpICs9IDEsIG8gKz0gMSkge1xuICAgICAgICB0aGlzLmJ1ZmZlci5fYnl0ZXNbb10gPSBieXRlc1tpXTtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgLy8gdm9pZCBzZXQoVHlwZWRBcnJheSBhcnJheSwgb3B0aW9uYWwgdW5zaWduZWQgbG9uZyBvZmZzZXQpO1xuICAgIC8vIHZvaWQgc2V0KHNlcXVlbmNlPHR5cGU+IGFycmF5LCBvcHRpb25hbCB1bnNpZ25lZCBsb25nIG9mZnNldCk7XG4gICAgY3Rvci5wcm90b3R5cGUuc2V0ID0gZnVuY3Rpb24oaW5kZXgsIHZhbHVlKSB7XG4gICAgICBpZiAoYXJndW1lbnRzLmxlbmd0aCA8IDEpIHRocm93IG5ldyBTeW50YXhFcnJvcihcIk5vdCBlbm91Z2ggYXJndW1lbnRzXCIpO1xuICAgICAgdmFyIGFycmF5LCBzZXF1ZW5jZSwgb2Zmc2V0LCBsZW4sXG4gICAgICAgICAgaSwgcywgZCxcbiAgICAgICAgICBieXRlT2Zmc2V0LCBieXRlTGVuZ3RoLCB0bXA7XG5cbiAgICAgIGlmICh0eXBlb2YgYXJndW1lbnRzWzBdID09PSAnb2JqZWN0JyAmJiBhcmd1bWVudHNbMF0uY29uc3RydWN0b3IgPT09IHRoaXMuY29uc3RydWN0b3IpIHtcbiAgICAgICAgLy8gdm9pZCBzZXQoVHlwZWRBcnJheSBhcnJheSwgb3B0aW9uYWwgdW5zaWduZWQgbG9uZyBvZmZzZXQpO1xuICAgICAgICBhcnJheSA9IGFyZ3VtZW50c1swXTtcbiAgICAgICAgb2Zmc2V0ID0gRUNNQVNjcmlwdC5Ub1VpbnQzMihhcmd1bWVudHNbMV0pO1xuXG4gICAgICAgIGlmIChvZmZzZXQgKyBhcnJheS5sZW5ndGggPiB0aGlzLmxlbmd0aCkge1xuICAgICAgICAgIHRocm93IG5ldyBSYW5nZUVycm9yKFwiT2Zmc2V0IHBsdXMgbGVuZ3RoIG9mIGFycmF5IGlzIG91dCBvZiByYW5nZVwiKTtcbiAgICAgICAgfVxuXG4gICAgICAgIGJ5dGVPZmZzZXQgPSB0aGlzLmJ5dGVPZmZzZXQgKyBvZmZzZXQgKiB0aGlzLkJZVEVTX1BFUl9FTEVNRU5UO1xuICAgICAgICBieXRlTGVuZ3RoID0gYXJyYXkubGVuZ3RoICogdGhpcy5CWVRFU19QRVJfRUxFTUVOVDtcblxuICAgICAgICBpZiAoYXJyYXkuYnVmZmVyID09PSB0aGlzLmJ1ZmZlcikge1xuICAgICAgICAgIHRtcCA9IFtdO1xuICAgICAgICAgIGZvciAoaSA9IDAsIHMgPSBhcnJheS5ieXRlT2Zmc2V0OyBpIDwgYnl0ZUxlbmd0aDsgaSArPSAxLCBzICs9IDEpIHtcbiAgICAgICAgICAgIHRtcFtpXSA9IGFycmF5LmJ1ZmZlci5fYnl0ZXNbc107XG4gICAgICAgICAgfVxuICAgICAgICAgIGZvciAoaSA9IDAsIGQgPSBieXRlT2Zmc2V0OyBpIDwgYnl0ZUxlbmd0aDsgaSArPSAxLCBkICs9IDEpIHtcbiAgICAgICAgICAgIHRoaXMuYnVmZmVyLl9ieXRlc1tkXSA9IHRtcFtpXTtcbiAgICAgICAgICB9XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgZm9yIChpID0gMCwgcyA9IGFycmF5LmJ5dGVPZmZzZXQsIGQgPSBieXRlT2Zmc2V0O1xuICAgICAgICAgICAgICAgaSA8IGJ5dGVMZW5ndGg7IGkgKz0gMSwgcyArPSAxLCBkICs9IDEpIHtcbiAgICAgICAgICAgIHRoaXMuYnVmZmVyLl9ieXRlc1tkXSA9IGFycmF5LmJ1ZmZlci5fYnl0ZXNbc107XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9IGVsc2UgaWYgKHR5cGVvZiBhcmd1bWVudHNbMF0gPT09ICdvYmplY3QnICYmIHR5cGVvZiBhcmd1bWVudHNbMF0ubGVuZ3RoICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAvLyB2b2lkIHNldChzZXF1ZW5jZTx0eXBlPiBhcnJheSwgb3B0aW9uYWwgdW5zaWduZWQgbG9uZyBvZmZzZXQpO1xuICAgICAgICBzZXF1ZW5jZSA9IGFyZ3VtZW50c1swXTtcbiAgICAgICAgbGVuID0gRUNNQVNjcmlwdC5Ub1VpbnQzMihzZXF1ZW5jZS5sZW5ndGgpO1xuICAgICAgICBvZmZzZXQgPSBFQ01BU2NyaXB0LlRvVWludDMyKGFyZ3VtZW50c1sxXSk7XG5cbiAgICAgICAgaWYgKG9mZnNldCArIGxlbiA+IHRoaXMubGVuZ3RoKSB7XG4gICAgICAgICAgdGhyb3cgbmV3IFJhbmdlRXJyb3IoXCJPZmZzZXQgcGx1cyBsZW5ndGggb2YgYXJyYXkgaXMgb3V0IG9mIHJhbmdlXCIpO1xuICAgICAgICB9XG5cbiAgICAgICAgZm9yIChpID0gMDsgaSA8IGxlbjsgaSArPSAxKSB7XG4gICAgICAgICAgcyA9IHNlcXVlbmNlW2ldO1xuICAgICAgICAgIHRoaXMuX3NldHRlcihvZmZzZXQgKyBpLCBOdW1iZXIocykpO1xuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0aHJvdyBuZXcgVHlwZUVycm9yKFwiVW5leHBlY3RlZCBhcmd1bWVudCB0eXBlKHMpXCIpO1xuICAgICAgfVxuICAgIH07XG5cbiAgICAvLyBUeXBlZEFycmF5IHN1YmFycmF5KGxvbmcgYmVnaW4sIG9wdGlvbmFsIGxvbmcgZW5kKTtcbiAgICBjdG9yLnByb3RvdHlwZS5zdWJhcnJheSA9IGZ1bmN0aW9uKHN0YXJ0LCBlbmQpIHtcbiAgICAgIGZ1bmN0aW9uIGNsYW1wKHYsIG1pbiwgbWF4KSB7IHJldHVybiB2IDwgbWluID8gbWluIDogdiA+IG1heCA/IG1heCA6IHY7IH1cblxuICAgICAgc3RhcnQgPSBFQ01BU2NyaXB0LlRvSW50MzIoc3RhcnQpO1xuICAgICAgZW5kID0gRUNNQVNjcmlwdC5Ub0ludDMyKGVuZCk7XG5cbiAgICAgIGlmIChhcmd1bWVudHMubGVuZ3RoIDwgMSkgeyBzdGFydCA9IDA7IH1cbiAgICAgIGlmIChhcmd1bWVudHMubGVuZ3RoIDwgMikgeyBlbmQgPSB0aGlzLmxlbmd0aDsgfVxuXG4gICAgICBpZiAoc3RhcnQgPCAwKSB7IHN0YXJ0ID0gdGhpcy5sZW5ndGggKyBzdGFydDsgfVxuICAgICAgaWYgKGVuZCA8IDApIHsgZW5kID0gdGhpcy5sZW5ndGggKyBlbmQ7IH1cblxuICAgICAgc3RhcnQgPSBjbGFtcChzdGFydCwgMCwgdGhpcy5sZW5ndGgpO1xuICAgICAgZW5kID0gY2xhbXAoZW5kLCAwLCB0aGlzLmxlbmd0aCk7XG5cbiAgICAgIHZhciBsZW4gPSBlbmQgLSBzdGFydDtcbiAgICAgIGlmIChsZW4gPCAwKSB7XG4gICAgICAgIGxlbiA9IDA7XG4gICAgICB9XG5cbiAgICAgIHJldHVybiBuZXcgdGhpcy5jb25zdHJ1Y3RvcihcbiAgICAgICAgdGhpcy5idWZmZXIsIHRoaXMuYnl0ZU9mZnNldCArIHN0YXJ0ICogdGhpcy5CWVRFU19QRVJfRUxFTUVOVCwgbGVuKTtcbiAgICB9O1xuXG4gICAgcmV0dXJuIGN0b3I7XG4gIH1cblxuICB2YXIgSW50OEFycmF5ID0gbWFrZUNvbnN0cnVjdG9yKDEsIHBhY2tJOCwgdW5wYWNrSTgpO1xuICB2YXIgVWludDhBcnJheSA9IG1ha2VDb25zdHJ1Y3RvcigxLCBwYWNrVTgsIHVucGFja1U4KTtcbiAgdmFyIFVpbnQ4Q2xhbXBlZEFycmF5ID0gbWFrZUNvbnN0cnVjdG9yKDEsIHBhY2tVOENsYW1wZWQsIHVucGFja1U4KTtcbiAgdmFyIEludDE2QXJyYXkgPSBtYWtlQ29uc3RydWN0b3IoMiwgcGFja0kxNiwgdW5wYWNrSTE2KTtcbiAgdmFyIFVpbnQxNkFycmF5ID0gbWFrZUNvbnN0cnVjdG9yKDIsIHBhY2tVMTYsIHVucGFja1UxNik7XG4gIHZhciBJbnQzMkFycmF5ID0gbWFrZUNvbnN0cnVjdG9yKDQsIHBhY2tJMzIsIHVucGFja0kzMik7XG4gIHZhciBVaW50MzJBcnJheSA9IG1ha2VDb25zdHJ1Y3Rvcig0LCBwYWNrVTMyLCB1bnBhY2tVMzIpO1xuICB2YXIgRmxvYXQzMkFycmF5ID0gbWFrZUNvbnN0cnVjdG9yKDQsIHBhY2tGMzIsIHVucGFja0YzMik7XG4gIHZhciBGbG9hdDY0QXJyYXkgPSBtYWtlQ29uc3RydWN0b3IoOCwgcGFja0Y2NCwgdW5wYWNrRjY0KTtcblxuICBleHBvcnRzLkludDhBcnJheSA9IGV4cG9ydHMuSW50OEFycmF5IHx8IEludDhBcnJheTtcbiAgZXhwb3J0cy5VaW50OEFycmF5ID0gZXhwb3J0cy5VaW50OEFycmF5IHx8IFVpbnQ4QXJyYXk7XG4gIGV4cG9ydHMuVWludDhDbGFtcGVkQXJyYXkgPSBleHBvcnRzLlVpbnQ4Q2xhbXBlZEFycmF5IHx8IFVpbnQ4Q2xhbXBlZEFycmF5O1xuICBleHBvcnRzLkludDE2QXJyYXkgPSBleHBvcnRzLkludDE2QXJyYXkgfHwgSW50MTZBcnJheTtcbiAgZXhwb3J0cy5VaW50MTZBcnJheSA9IGV4cG9ydHMuVWludDE2QXJyYXkgfHwgVWludDE2QXJyYXk7XG4gIGV4cG9ydHMuSW50MzJBcnJheSA9IGV4cG9ydHMuSW50MzJBcnJheSB8fCBJbnQzMkFycmF5O1xuICBleHBvcnRzLlVpbnQzMkFycmF5ID0gZXhwb3J0cy5VaW50MzJBcnJheSB8fCBVaW50MzJBcnJheTtcbiAgZXhwb3J0cy5GbG9hdDMyQXJyYXkgPSBleHBvcnRzLkZsb2F0MzJBcnJheSB8fCBGbG9hdDMyQXJyYXk7XG4gIGV4cG9ydHMuRmxvYXQ2NEFycmF5ID0gZXhwb3J0cy5GbG9hdDY0QXJyYXkgfHwgRmxvYXQ2NEFycmF5O1xufSgpKTtcblxuLy9cbi8vIDYgVGhlIERhdGFWaWV3IFZpZXcgVHlwZVxuLy9cblxuKGZ1bmN0aW9uKCkge1xuICBmdW5jdGlvbiByKGFycmF5LCBpbmRleCkge1xuICAgIHJldHVybiBFQ01BU2NyaXB0LklzQ2FsbGFibGUoYXJyYXkuZ2V0KSA/IGFycmF5LmdldChpbmRleCkgOiBhcnJheVtpbmRleF07XG4gIH1cblxuICB2YXIgSVNfQklHX0VORElBTiA9IChmdW5jdGlvbigpIHtcbiAgICB2YXIgdTE2YXJyYXkgPSBuZXcoZXhwb3J0cy5VaW50MTZBcnJheSkoWzB4MTIzNF0pLFxuICAgICAgICB1OGFycmF5ID0gbmV3KGV4cG9ydHMuVWludDhBcnJheSkodTE2YXJyYXkuYnVmZmVyKTtcbiAgICByZXR1cm4gcih1OGFycmF5LCAwKSA9PT0gMHgxMjtcbiAgfSgpKTtcblxuICAvLyBDb25zdHJ1Y3RvcihBcnJheUJ1ZmZlciBidWZmZXIsXG4gIC8vICAgICAgICAgICAgIG9wdGlvbmFsIHVuc2lnbmVkIGxvbmcgYnl0ZU9mZnNldCxcbiAgLy8gICAgICAgICAgICAgb3B0aW9uYWwgdW5zaWduZWQgbG9uZyBieXRlTGVuZ3RoKVxuICAvKiogQGNvbnN0cnVjdG9yICovXG4gIHZhciBEYXRhVmlldyA9IGZ1bmN0aW9uIERhdGFWaWV3KGJ1ZmZlciwgYnl0ZU9mZnNldCwgYnl0ZUxlbmd0aCkge1xuICAgIGlmIChhcmd1bWVudHMubGVuZ3RoID09PSAwKSB7XG4gICAgICBidWZmZXIgPSBuZXcgZXhwb3J0cy5BcnJheUJ1ZmZlcigwKTtcbiAgICB9IGVsc2UgaWYgKCEoYnVmZmVyIGluc3RhbmNlb2YgZXhwb3J0cy5BcnJheUJ1ZmZlciB8fCBFQ01BU2NyaXB0LkNsYXNzKGJ1ZmZlcikgPT09ICdBcnJheUJ1ZmZlcicpKSB7XG4gICAgICB0aHJvdyBuZXcgVHlwZUVycm9yKFwiVHlwZUVycm9yXCIpO1xuICAgIH1cblxuICAgIHRoaXMuYnVmZmVyID0gYnVmZmVyIHx8IG5ldyBleHBvcnRzLkFycmF5QnVmZmVyKDApO1xuXG4gICAgdGhpcy5ieXRlT2Zmc2V0ID0gRUNNQVNjcmlwdC5Ub1VpbnQzMihieXRlT2Zmc2V0KTtcbiAgICBpZiAodGhpcy5ieXRlT2Zmc2V0ID4gdGhpcy5idWZmZXIuYnl0ZUxlbmd0aCkge1xuICAgICAgdGhyb3cgbmV3IFJhbmdlRXJyb3IoXCJieXRlT2Zmc2V0IG91dCBvZiByYW5nZVwiKTtcbiAgICB9XG5cbiAgICBpZiAoYXJndW1lbnRzLmxlbmd0aCA8IDMpIHtcbiAgICAgIHRoaXMuYnl0ZUxlbmd0aCA9IHRoaXMuYnVmZmVyLmJ5dGVMZW5ndGggLSB0aGlzLmJ5dGVPZmZzZXQ7XG4gICAgfSBlbHNlIHtcbiAgICAgIHRoaXMuYnl0ZUxlbmd0aCA9IEVDTUFTY3JpcHQuVG9VaW50MzIoYnl0ZUxlbmd0aCk7XG4gICAgfVxuXG4gICAgaWYgKCh0aGlzLmJ5dGVPZmZzZXQgKyB0aGlzLmJ5dGVMZW5ndGgpID4gdGhpcy5idWZmZXIuYnl0ZUxlbmd0aCkge1xuICAgICAgdGhyb3cgbmV3IFJhbmdlRXJyb3IoXCJieXRlT2Zmc2V0IGFuZCBsZW5ndGggcmVmZXJlbmNlIGFuIGFyZWEgYmV5b25kIHRoZSBlbmQgb2YgdGhlIGJ1ZmZlclwiKTtcbiAgICB9XG5cbiAgICBjb25maWd1cmVQcm9wZXJ0aWVzKHRoaXMpO1xuICB9O1xuXG4gIGZ1bmN0aW9uIG1ha2VHZXR0ZXIoYXJyYXlUeXBlKSB7XG4gICAgcmV0dXJuIGZ1bmN0aW9uKGJ5dGVPZmZzZXQsIGxpdHRsZUVuZGlhbikge1xuXG4gICAgICBieXRlT2Zmc2V0ID0gRUNNQVNjcmlwdC5Ub1VpbnQzMihieXRlT2Zmc2V0KTtcblxuICAgICAgaWYgKGJ5dGVPZmZzZXQgKyBhcnJheVR5cGUuQllURVNfUEVSX0VMRU1FTlQgPiB0aGlzLmJ5dGVMZW5ndGgpIHtcbiAgICAgICAgdGhyb3cgbmV3IFJhbmdlRXJyb3IoXCJBcnJheSBpbmRleCBvdXQgb2YgcmFuZ2VcIik7XG4gICAgICB9XG4gICAgICBieXRlT2Zmc2V0ICs9IHRoaXMuYnl0ZU9mZnNldDtcblxuICAgICAgdmFyIHVpbnQ4QXJyYXkgPSBuZXcgZXhwb3J0cy5VaW50OEFycmF5KHRoaXMuYnVmZmVyLCBieXRlT2Zmc2V0LCBhcnJheVR5cGUuQllURVNfUEVSX0VMRU1FTlQpLFxuICAgICAgICAgIGJ5dGVzID0gW10sIGk7XG4gICAgICBmb3IgKGkgPSAwOyBpIDwgYXJyYXlUeXBlLkJZVEVTX1BFUl9FTEVNRU5UOyBpICs9IDEpIHtcbiAgICAgICAgYnl0ZXMucHVzaChyKHVpbnQ4QXJyYXksIGkpKTtcbiAgICAgIH1cblxuICAgICAgaWYgKEJvb2xlYW4obGl0dGxlRW5kaWFuKSA9PT0gQm9vbGVhbihJU19CSUdfRU5ESUFOKSkge1xuICAgICAgICBieXRlcy5yZXZlcnNlKCk7XG4gICAgICB9XG5cbiAgICAgIHJldHVybiByKG5ldyBhcnJheVR5cGUobmV3IGV4cG9ydHMuVWludDhBcnJheShieXRlcykuYnVmZmVyKSwgMCk7XG4gICAgfTtcbiAgfVxuXG4gIERhdGFWaWV3LnByb3RvdHlwZS5nZXRVaW50OCA9IG1ha2VHZXR0ZXIoZXhwb3J0cy5VaW50OEFycmF5KTtcbiAgRGF0YVZpZXcucHJvdG90eXBlLmdldEludDggPSBtYWtlR2V0dGVyKGV4cG9ydHMuSW50OEFycmF5KTtcbiAgRGF0YVZpZXcucHJvdG90eXBlLmdldFVpbnQxNiA9IG1ha2VHZXR0ZXIoZXhwb3J0cy5VaW50MTZBcnJheSk7XG4gIERhdGFWaWV3LnByb3RvdHlwZS5nZXRJbnQxNiA9IG1ha2VHZXR0ZXIoZXhwb3J0cy5JbnQxNkFycmF5KTtcbiAgRGF0YVZpZXcucHJvdG90eXBlLmdldFVpbnQzMiA9IG1ha2VHZXR0ZXIoZXhwb3J0cy5VaW50MzJBcnJheSk7XG4gIERhdGFWaWV3LnByb3RvdHlwZS5nZXRJbnQzMiA9IG1ha2VHZXR0ZXIoZXhwb3J0cy5JbnQzMkFycmF5KTtcbiAgRGF0YVZpZXcucHJvdG90eXBlLmdldEZsb2F0MzIgPSBtYWtlR2V0dGVyKGV4cG9ydHMuRmxvYXQzMkFycmF5KTtcbiAgRGF0YVZpZXcucHJvdG90eXBlLmdldEZsb2F0NjQgPSBtYWtlR2V0dGVyKGV4cG9ydHMuRmxvYXQ2NEFycmF5KTtcblxuICBmdW5jdGlvbiBtYWtlU2V0dGVyKGFycmF5VHlwZSkge1xuICAgIHJldHVybiBmdW5jdGlvbihieXRlT2Zmc2V0LCB2YWx1ZSwgbGl0dGxlRW5kaWFuKSB7XG5cbiAgICAgIGJ5dGVPZmZzZXQgPSBFQ01BU2NyaXB0LlRvVWludDMyKGJ5dGVPZmZzZXQpO1xuICAgICAgaWYgKGJ5dGVPZmZzZXQgKyBhcnJheVR5cGUuQllURVNfUEVSX0VMRU1FTlQgPiB0aGlzLmJ5dGVMZW5ndGgpIHtcbiAgICAgICAgdGhyb3cgbmV3IFJhbmdlRXJyb3IoXCJBcnJheSBpbmRleCBvdXQgb2YgcmFuZ2VcIik7XG4gICAgICB9XG5cbiAgICAgIC8vIEdldCBieXRlc1xuICAgICAgdmFyIHR5cGVBcnJheSA9IG5ldyBhcnJheVR5cGUoW3ZhbHVlXSksXG4gICAgICAgICAgYnl0ZUFycmF5ID0gbmV3IGV4cG9ydHMuVWludDhBcnJheSh0eXBlQXJyYXkuYnVmZmVyKSxcbiAgICAgICAgICBieXRlcyA9IFtdLCBpLCBieXRlVmlldztcblxuICAgICAgZm9yIChpID0gMDsgaSA8IGFycmF5VHlwZS5CWVRFU19QRVJfRUxFTUVOVDsgaSArPSAxKSB7XG4gICAgICAgIGJ5dGVzLnB1c2gocihieXRlQXJyYXksIGkpKTtcbiAgICAgIH1cblxuICAgICAgLy8gRmxpcCBpZiBuZWNlc3NhcnlcbiAgICAgIGlmIChCb29sZWFuKGxpdHRsZUVuZGlhbikgPT09IEJvb2xlYW4oSVNfQklHX0VORElBTikpIHtcbiAgICAgICAgYnl0ZXMucmV2ZXJzZSgpO1xuICAgICAgfVxuXG4gICAgICAvLyBXcml0ZSB0aGVtXG4gICAgICBieXRlVmlldyA9IG5ldyBleHBvcnRzLlVpbnQ4QXJyYXkodGhpcy5idWZmZXIsIGJ5dGVPZmZzZXQsIGFycmF5VHlwZS5CWVRFU19QRVJfRUxFTUVOVCk7XG4gICAgICBieXRlVmlldy5zZXQoYnl0ZXMpO1xuICAgIH07XG4gIH1cblxuICBEYXRhVmlldy5wcm90b3R5cGUuc2V0VWludDggPSBtYWtlU2V0dGVyKGV4cG9ydHMuVWludDhBcnJheSk7XG4gIERhdGFWaWV3LnByb3RvdHlwZS5zZXRJbnQ4ID0gbWFrZVNldHRlcihleHBvcnRzLkludDhBcnJheSk7XG4gIERhdGFWaWV3LnByb3RvdHlwZS5zZXRVaW50MTYgPSBtYWtlU2V0dGVyKGV4cG9ydHMuVWludDE2QXJyYXkpO1xuICBEYXRhVmlldy5wcm90b3R5cGUuc2V0SW50MTYgPSBtYWtlU2V0dGVyKGV4cG9ydHMuSW50MTZBcnJheSk7XG4gIERhdGFWaWV3LnByb3RvdHlwZS5zZXRVaW50MzIgPSBtYWtlU2V0dGVyKGV4cG9ydHMuVWludDMyQXJyYXkpO1xuICBEYXRhVmlldy5wcm90b3R5cGUuc2V0SW50MzIgPSBtYWtlU2V0dGVyKGV4cG9ydHMuSW50MzJBcnJheSk7XG4gIERhdGFWaWV3LnByb3RvdHlwZS5zZXRGbG9hdDMyID0gbWFrZVNldHRlcihleHBvcnRzLkZsb2F0MzJBcnJheSk7XG4gIERhdGFWaWV3LnByb3RvdHlwZS5zZXRGbG9hdDY0ID0gbWFrZVNldHRlcihleHBvcnRzLkZsb2F0NjRBcnJheSk7XG5cbiAgZXhwb3J0cy5EYXRhVmlldyA9IGV4cG9ydHMuRGF0YVZpZXcgfHwgRGF0YVZpZXc7XG5cbn0oKSk7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/typedarray/index.js\n");

/***/ })

};
;