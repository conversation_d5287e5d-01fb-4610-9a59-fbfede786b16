"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/compressible";
exports.ids = ["vendor-chunks/compressible"];
exports.modules = {

/***/ "(rsc)/./node_modules/compressible/index.js":
/*!********************************************!*\
  !*** ./node_modules/compressible/index.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/*!\n * compressible\n * Copyright(c) 2013 Jonathan Ong\n * Copyright(c) 2014 Jeremiah Senkpiel\n * Copyright(c) 2015 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\n/**\n * Module dependencies.\n * @private\n */\n\nvar db = __webpack_require__(/*! mime-db */ \"(rsc)/./node_modules/mime-db/index.js\")\n\n/**\n * Module variables.\n * @private\n */\n\nvar COMPRESSIBLE_TYPE_REGEXP = /^text\\/|\\+(?:json|text|xml)$/i\nvar EXTRACT_TYPE_REGEXP = /^\\s*([^;\\s]*)(?:;|\\s|$)/\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = compressible\n\n/**\n * Checks if a type is compressible.\n *\n * @param {string} type\n * @return {Boolean} compressible\n * @public\n */\n\nfunction compressible (type) {\n  if (!type || typeof type !== 'string') {\n    return false\n  }\n\n  // strip parameters\n  var match = EXTRACT_TYPE_REGEXP.exec(type)\n  var mime = match && match[1].toLowerCase()\n  var data = db[mime]\n\n  // return database information\n  if (data && data.compressible !== undefined) {\n    return data.compressible\n  }\n\n  // fallback to regexp or unknown\n  return COMPRESSIBLE_TYPE_REGEXP.test(mime) || undefined\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/compressible/index.js\n");

/***/ })

};
;