"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/on-headers";
exports.ids = ["vendor-chunks/on-headers"];
exports.modules = {

/***/ "(rsc)/./node_modules/on-headers/index.js":
/*!******************************************!*\
  !*** ./node_modules/on-headers/index.js ***!
  \******************************************/
/***/ ((module) => {

eval("/*!\n * on-headers\n * Copyright(c) 2014 Douglas Christopher <PERSON>\n * MIT Licensed\n */\n\n\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = onHeaders\n\n/**\n * Create a replacement writeHead method.\n *\n * @param {function} prevWriteHead\n * @param {function} listener\n * @private\n */\n\nfunction createWriteHead (prevWriteHead, listener) {\n  var fired = false\n\n  // return function with core name and argument list\n  return function writeHead (statusCode) {\n    // set headers from arguments\n    var args = setWriteHeadHeaders.apply(this, arguments)\n\n    // fire listener\n    if (!fired) {\n      fired = true\n      listener.call(this)\n\n      // pass-along an updated status code\n      if (typeof args[0] === 'number' && this.statusCode !== args[0]) {\n        args[0] = this.statusCode\n        args.length = 1\n      }\n    }\n\n    return prevWriteHead.apply(this, args)\n  }\n}\n\n/**\n * Execute a listener when a response is about to write headers.\n *\n * @param {object} res\n * @return {function} listener\n * @public\n */\n\nfunction onHeaders (res, listener) {\n  if (!res) {\n    throw new TypeError('argument res is required')\n  }\n\n  if (typeof listener !== 'function') {\n    throw new TypeError('argument listener must be a function')\n  }\n\n  res.writeHead = createWriteHead(res.writeHead, listener)\n}\n\n/**\n * Set headers contained in array on the response object.\n *\n * @param {object} res\n * @param {array} headers\n * @private\n */\n\nfunction setHeadersFromArray (res, headers) {\n  for (var i = 0; i < headers.length; i++) {\n    res.setHeader(headers[i][0], headers[i][1])\n  }\n}\n\n/**\n * Set headers contained in object on the response object.\n *\n * @param {object} res\n * @param {object} headers\n * @private\n */\n\nfunction setHeadersFromObject (res, headers) {\n  var keys = Object.keys(headers)\n  for (var i = 0; i < keys.length; i++) {\n    var k = keys[i]\n    if (k) res.setHeader(k, headers[k])\n  }\n}\n\n/**\n * Set headers and other properties on the response object.\n *\n * @param {number} statusCode\n * @private\n */\n\nfunction setWriteHeadHeaders (statusCode) {\n  var length = arguments.length\n  var headerIndex = length > 1 && typeof arguments[1] === 'string'\n    ? 2\n    : 1\n\n  var headers = length >= headerIndex + 1\n    ? arguments[headerIndex]\n    : undefined\n\n  this.statusCode = statusCode\n\n  if (Array.isArray(headers)) {\n    // handle array case\n    setHeadersFromArray(this, headers)\n  } else if (headers) {\n    // handle object case\n    setHeadersFromObject(this, headers)\n  }\n\n  // copy leading arguments\n  var args = new Array(Math.min(length, headerIndex))\n  for (var i = 0; i < args.length; i++) {\n    args[i] = arguments[i]\n  }\n\n  return args\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/on-headers/index.js\n");

/***/ })

};
;