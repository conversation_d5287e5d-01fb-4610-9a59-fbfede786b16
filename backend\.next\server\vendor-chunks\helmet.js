"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/helmet";
exports.ids = ["vendor-chunks/helmet"];
exports.modules = {

/***/ "(rsc)/./node_modules/helmet/index.mjs":
/*!***************************************!*\
  !*** ./node_modules/helmet/index.mjs ***!
  \***************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   contentSecurityPolicy: () => (/* binding */ contentSecurityPolicy),\n/* harmony export */   crossOriginEmbedderPolicy: () => (/* binding */ crossOriginEmbedderPolicy),\n/* harmony export */   crossOriginOpenerPolicy: () => (/* binding */ crossOriginOpenerPolicy),\n/* harmony export */   crossOriginResourcePolicy: () => (/* binding */ crossOriginResourcePolicy),\n/* harmony export */   \"default\": () => (/* binding */ helmet),\n/* harmony export */   dnsPrefetchControl: () => (/* binding */ xDnsPrefetchControl),\n/* harmony export */   frameguard: () => (/* binding */ xFrameOptions),\n/* harmony export */   hidePoweredBy: () => (/* binding */ xPoweredBy),\n/* harmony export */   hsts: () => (/* binding */ strictTransportSecurity),\n/* harmony export */   ieNoOpen: () => (/* binding */ xDownloadOptions),\n/* harmony export */   noSniff: () => (/* binding */ xContentTypeOptions),\n/* harmony export */   originAgentCluster: () => (/* binding */ originAgentCluster),\n/* harmony export */   permittedCrossDomainPolicies: () => (/* binding */ xPermittedCrossDomainPolicies),\n/* harmony export */   referrerPolicy: () => (/* binding */ referrerPolicy),\n/* harmony export */   strictTransportSecurity: () => (/* binding */ strictTransportSecurity),\n/* harmony export */   xContentTypeOptions: () => (/* binding */ xContentTypeOptions),\n/* harmony export */   xDnsPrefetchControl: () => (/* binding */ xDnsPrefetchControl),\n/* harmony export */   xDownloadOptions: () => (/* binding */ xDownloadOptions),\n/* harmony export */   xFrameOptions: () => (/* binding */ xFrameOptions),\n/* harmony export */   xPermittedCrossDomainPolicies: () => (/* binding */ xPermittedCrossDomainPolicies),\n/* harmony export */   xPoweredBy: () => (/* binding */ xPoweredBy),\n/* harmony export */   xXssProtection: () => (/* binding */ xXssProtection),\n/* harmony export */   xssFilter: () => (/* binding */ xXssProtection)\n/* harmony export */ });\nconst dangerouslyDisableDefaultSrc = Symbol(\"dangerouslyDisableDefaultSrc\")\nconst DEFAULT_DIRECTIVES = {\n\t\"default-src\": [\"'self'\"],\n\t\"base-uri\": [\"'self'\"],\n\t\"font-src\": [\"'self'\", \"https:\", \"data:\"],\n\t\"form-action\": [\"'self'\"],\n\t\"frame-ancestors\": [\"'self'\"],\n\t\"img-src\": [\"'self'\", \"data:\"],\n\t\"object-src\": [\"'none'\"],\n\t\"script-src\": [\"'self'\"],\n\t\"script-src-attr\": [\"'none'\"],\n\t\"style-src\": [\"'self'\", \"https:\", \"'unsafe-inline'\"],\n\t\"upgrade-insecure-requests\": []\n}\nconst SHOULD_BE_QUOTED = new Set([\"none\", \"self\", \"strict-dynamic\", \"report-sample\", \"inline-speculation-rules\", \"unsafe-inline\", \"unsafe-eval\", \"unsafe-hashes\", \"wasm-unsafe-eval\"])\nconst getDefaultDirectives = () => Object.assign({}, DEFAULT_DIRECTIVES)\nconst dashify = str => str.replace(/[A-Z]/g, capitalLetter => \"-\" + capitalLetter.toLowerCase())\nconst isDirectiveValueInvalid = directiveValue => /;|,/.test(directiveValue)\nconst shouldDirectiveValueEntryBeQuoted = directiveValueEntry => SHOULD_BE_QUOTED.has(directiveValueEntry) || directiveValueEntry.startsWith(\"nonce-\") || directiveValueEntry.startsWith(\"sha256-\") || directiveValueEntry.startsWith(\"sha384-\") || directiveValueEntry.startsWith(\"sha512-\")\nconst warnIfDirectiveValueEntryShouldBeQuoted = value => {\n\tif (shouldDirectiveValueEntryBeQuoted(value)) {\n\t\tconsole.warn(`Content-Security-Policy got directive value \\`${value}\\` which should be single-quoted and changed to \\`'${value}'\\`. This will be an error in future versions of Helmet.`)\n\t}\n}\nconst has = (obj, key) => Object.prototype.hasOwnProperty.call(obj, key)\nfunction normalizeDirectives(options) {\n\tconst defaultDirectives = getDefaultDirectives()\n\tconst {useDefaults = true, directives: rawDirectives = defaultDirectives} = options\n\tconst result = new Map()\n\tconst directiveNamesSeen = new Set()\n\tconst directivesExplicitlyDisabled = new Set()\n\tfor (const rawDirectiveName in rawDirectives) {\n\t\tif (!has(rawDirectives, rawDirectiveName)) {\n\t\t\tcontinue\n\t\t}\n\t\tif (rawDirectiveName.length === 0 || /[^a-zA-Z0-9-]/.test(rawDirectiveName)) {\n\t\t\tthrow new Error(`Content-Security-Policy received an invalid directive name ${JSON.stringify(rawDirectiveName)}`)\n\t\t}\n\t\tconst directiveName = dashify(rawDirectiveName)\n\t\tif (directiveNamesSeen.has(directiveName)) {\n\t\t\tthrow new Error(`Content-Security-Policy received a duplicate directive ${JSON.stringify(directiveName)}`)\n\t\t}\n\t\tdirectiveNamesSeen.add(directiveName)\n\t\tconst rawDirectiveValue = rawDirectives[rawDirectiveName]\n\t\tlet directiveValue\n\t\tif (rawDirectiveValue === null) {\n\t\t\tif (directiveName === \"default-src\") {\n\t\t\t\tthrow new Error(\"Content-Security-Policy needs a default-src but it was set to `null`. If you really want to disable it, set it to `contentSecurityPolicy.dangerouslyDisableDefaultSrc`.\")\n\t\t\t}\n\t\t\tdirectivesExplicitlyDisabled.add(directiveName)\n\t\t\tcontinue\n\t\t} else if (typeof rawDirectiveValue === \"string\") {\n\t\t\tdirectiveValue = [rawDirectiveValue]\n\t\t} else if (!rawDirectiveValue) {\n\t\t\tthrow new Error(`Content-Security-Policy received an invalid directive value for ${JSON.stringify(directiveName)}`)\n\t\t} else if (rawDirectiveValue === dangerouslyDisableDefaultSrc) {\n\t\t\tif (directiveName === \"default-src\") {\n\t\t\t\tdirectivesExplicitlyDisabled.add(\"default-src\")\n\t\t\t\tcontinue\n\t\t\t} else {\n\t\t\t\tthrow new Error(`Content-Security-Policy: tried to disable ${JSON.stringify(directiveName)} as if it were default-src; simply omit the key`)\n\t\t\t}\n\t\t} else {\n\t\t\tdirectiveValue = rawDirectiveValue\n\t\t}\n\t\tfor (const element of directiveValue) {\n\t\t\tif (typeof element === \"string\") {\n\t\t\t\tif (isDirectiveValueInvalid(element)) {\n\t\t\t\t\tthrow new Error(`Content-Security-Policy received an invalid directive value for ${JSON.stringify(directiveName)}`)\n\t\t\t\t}\n\t\t\t\twarnIfDirectiveValueEntryShouldBeQuoted(element)\n\t\t\t}\n\t\t}\n\t\tresult.set(directiveName, directiveValue)\n\t}\n\tif (useDefaults) {\n\t\tObject.entries(defaultDirectives).forEach(([defaultDirectiveName, defaultDirectiveValue]) => {\n\t\t\tif (!result.has(defaultDirectiveName) && !directivesExplicitlyDisabled.has(defaultDirectiveName)) {\n\t\t\t\tresult.set(defaultDirectiveName, defaultDirectiveValue)\n\t\t\t}\n\t\t})\n\t}\n\tif (!result.size) {\n\t\tthrow new Error(\"Content-Security-Policy has no directives. Either set some or disable the header\")\n\t}\n\tif (!result.has(\"default-src\") && !directivesExplicitlyDisabled.has(\"default-src\")) {\n\t\tthrow new Error(\"Content-Security-Policy needs a default-src but none was provided. If you really want to disable it, set it to `contentSecurityPolicy.dangerouslyDisableDefaultSrc`.\")\n\t}\n\treturn result\n}\nfunction getHeaderValue(req, res, normalizedDirectives) {\n\tlet err\n\tconst result = []\n\tnormalizedDirectives.forEach((rawDirectiveValue, directiveName) => {\n\t\tlet directiveValue = \"\"\n\t\tfor (const element of rawDirectiveValue) {\n\t\t\tif (typeof element === \"function\") {\n\t\t\t\tconst newElement = element(req, res)\n\t\t\t\twarnIfDirectiveValueEntryShouldBeQuoted(newElement)\n\t\t\t\tdirectiveValue += \" \" + newElement\n\t\t\t} else {\n\t\t\t\tdirectiveValue += \" \" + element\n\t\t\t}\n\t\t}\n\t\tif (!directiveValue) {\n\t\t\tresult.push(directiveName)\n\t\t} else if (isDirectiveValueInvalid(directiveValue)) {\n\t\t\terr = new Error(`Content-Security-Policy received an invalid directive value for ${JSON.stringify(directiveName)}`)\n\t\t} else {\n\t\t\tresult.push(`${directiveName}${directiveValue}`)\n\t\t}\n\t})\n\treturn err ? err : result.join(\";\")\n}\nconst contentSecurityPolicy = function contentSecurityPolicy(options = {}) {\n\tconst headerName = options.reportOnly ? \"Content-Security-Policy-Report-Only\" : \"Content-Security-Policy\"\n\tconst normalizedDirectives = normalizeDirectives(options)\n\treturn function contentSecurityPolicyMiddleware(req, res, next) {\n\t\tconst result = getHeaderValue(req, res, normalizedDirectives)\n\t\tif (result instanceof Error) {\n\t\t\tnext(result)\n\t\t} else {\n\t\t\tres.setHeader(headerName, result)\n\t\t\tnext()\n\t\t}\n\t}\n}\ncontentSecurityPolicy.getDefaultDirectives = getDefaultDirectives\ncontentSecurityPolicy.dangerouslyDisableDefaultSrc = dangerouslyDisableDefaultSrc\n\nconst ALLOWED_POLICIES$2 = new Set([\"require-corp\", \"credentialless\", \"unsafe-none\"])\nfunction getHeaderValueFromOptions$6({policy = \"require-corp\"}) {\n\tif (ALLOWED_POLICIES$2.has(policy)) {\n\t\treturn policy\n\t} else {\n\t\tthrow new Error(`Cross-Origin-Embedder-Policy does not support the ${JSON.stringify(policy)} policy`)\n\t}\n}\nfunction crossOriginEmbedderPolicy(options = {}) {\n\tconst headerValue = getHeaderValueFromOptions$6(options)\n\treturn function crossOriginEmbedderPolicyMiddleware(_req, res, next) {\n\t\tres.setHeader(\"Cross-Origin-Embedder-Policy\", headerValue)\n\t\tnext()\n\t}\n}\n\nconst ALLOWED_POLICIES$1 = new Set([\"same-origin\", \"same-origin-allow-popups\", \"unsafe-none\"])\nfunction getHeaderValueFromOptions$5({policy = \"same-origin\"}) {\n\tif (ALLOWED_POLICIES$1.has(policy)) {\n\t\treturn policy\n\t} else {\n\t\tthrow new Error(`Cross-Origin-Opener-Policy does not support the ${JSON.stringify(policy)} policy`)\n\t}\n}\nfunction crossOriginOpenerPolicy(options = {}) {\n\tconst headerValue = getHeaderValueFromOptions$5(options)\n\treturn function crossOriginOpenerPolicyMiddleware(_req, res, next) {\n\t\tres.setHeader(\"Cross-Origin-Opener-Policy\", headerValue)\n\t\tnext()\n\t}\n}\n\nconst ALLOWED_POLICIES = new Set([\"same-origin\", \"same-site\", \"cross-origin\"])\nfunction getHeaderValueFromOptions$4({policy = \"same-origin\"}) {\n\tif (ALLOWED_POLICIES.has(policy)) {\n\t\treturn policy\n\t} else {\n\t\tthrow new Error(`Cross-Origin-Resource-Policy does not support the ${JSON.stringify(policy)} policy`)\n\t}\n}\nfunction crossOriginResourcePolicy(options = {}) {\n\tconst headerValue = getHeaderValueFromOptions$4(options)\n\treturn function crossOriginResourcePolicyMiddleware(_req, res, next) {\n\t\tres.setHeader(\"Cross-Origin-Resource-Policy\", headerValue)\n\t\tnext()\n\t}\n}\n\nfunction originAgentCluster() {\n\treturn function originAgentClusterMiddleware(_req, res, next) {\n\t\tres.setHeader(\"Origin-Agent-Cluster\", \"?1\")\n\t\tnext()\n\t}\n}\n\nconst ALLOWED_TOKENS = new Set([\"no-referrer\", \"no-referrer-when-downgrade\", \"same-origin\", \"origin\", \"strict-origin\", \"origin-when-cross-origin\", \"strict-origin-when-cross-origin\", \"unsafe-url\", \"\"])\nfunction getHeaderValueFromOptions$3({policy = [\"no-referrer\"]}) {\n\tconst tokens = typeof policy === \"string\" ? [policy] : policy\n\tif (tokens.length === 0) {\n\t\tthrow new Error(\"Referrer-Policy received no policy tokens\")\n\t}\n\tconst tokensSeen = new Set()\n\ttokens.forEach(token => {\n\t\tif (!ALLOWED_TOKENS.has(token)) {\n\t\t\tthrow new Error(`Referrer-Policy received an unexpected policy token ${JSON.stringify(token)}`)\n\t\t} else if (tokensSeen.has(token)) {\n\t\t\tthrow new Error(`Referrer-Policy received a duplicate policy token ${JSON.stringify(token)}`)\n\t\t}\n\t\ttokensSeen.add(token)\n\t})\n\treturn tokens.join(\",\")\n}\nfunction referrerPolicy(options = {}) {\n\tconst headerValue = getHeaderValueFromOptions$3(options)\n\treturn function referrerPolicyMiddleware(_req, res, next) {\n\t\tres.setHeader(\"Referrer-Policy\", headerValue)\n\t\tnext()\n\t}\n}\n\nconst DEFAULT_MAX_AGE = 180 * 24 * 60 * 60\nfunction parseMaxAge(value = DEFAULT_MAX_AGE) {\n\tif (value >= 0 && Number.isFinite(value)) {\n\t\treturn Math.floor(value)\n\t} else {\n\t\tthrow new Error(`Strict-Transport-Security: ${JSON.stringify(value)} is not a valid value for maxAge. Please choose a positive integer.`)\n\t}\n}\nfunction getHeaderValueFromOptions$2(options) {\n\tif (\"maxage\" in options) {\n\t\tthrow new Error(\"Strict-Transport-Security received an unsupported property, `maxage`. Did you mean to pass `maxAge`?\")\n\t}\n\tif (\"includeSubdomains\" in options) {\n\t\tconsole.warn('Strict-Transport-Security middleware should use `includeSubDomains` instead of `includeSubdomains`. (The correct one has an uppercase \"D\".)')\n\t}\n\tconst directives = [`max-age=${parseMaxAge(options.maxAge)}`]\n\tif (options.includeSubDomains === undefined || options.includeSubDomains) {\n\t\tdirectives.push(\"includeSubDomains\")\n\t}\n\tif (options.preload) {\n\t\tdirectives.push(\"preload\")\n\t}\n\treturn directives.join(\"; \")\n}\nfunction strictTransportSecurity(options = {}) {\n\tconst headerValue = getHeaderValueFromOptions$2(options)\n\treturn function strictTransportSecurityMiddleware(_req, res, next) {\n\t\tres.setHeader(\"Strict-Transport-Security\", headerValue)\n\t\tnext()\n\t}\n}\n\nfunction xContentTypeOptions() {\n\treturn function xContentTypeOptionsMiddleware(_req, res, next) {\n\t\tres.setHeader(\"X-Content-Type-Options\", \"nosniff\")\n\t\tnext()\n\t}\n}\n\nfunction xDnsPrefetchControl(options = {}) {\n\tconst headerValue = options.allow ? \"on\" : \"off\"\n\treturn function xDnsPrefetchControlMiddleware(_req, res, next) {\n\t\tres.setHeader(\"X-DNS-Prefetch-Control\", headerValue)\n\t\tnext()\n\t}\n}\n\nfunction xDownloadOptions() {\n\treturn function xDownloadOptionsMiddleware(_req, res, next) {\n\t\tres.setHeader(\"X-Download-Options\", \"noopen\")\n\t\tnext()\n\t}\n}\n\nfunction getHeaderValueFromOptions$1({action = \"sameorigin\"}) {\n\tconst normalizedAction = typeof action === \"string\" ? action.toUpperCase() : action\n\tswitch (normalizedAction) {\n\t\tcase \"SAME-ORIGIN\":\n\t\t\treturn \"SAMEORIGIN\"\n\t\tcase \"DENY\":\n\t\tcase \"SAMEORIGIN\":\n\t\t\treturn normalizedAction\n\t\tdefault:\n\t\t\tthrow new Error(`X-Frame-Options received an invalid action ${JSON.stringify(action)}`)\n\t}\n}\nfunction xFrameOptions(options = {}) {\n\tconst headerValue = getHeaderValueFromOptions$1(options)\n\treturn function xFrameOptionsMiddleware(_req, res, next) {\n\t\tres.setHeader(\"X-Frame-Options\", headerValue)\n\t\tnext()\n\t}\n}\n\nconst ALLOWED_PERMITTED_POLICIES = new Set([\"none\", \"master-only\", \"by-content-type\", \"all\"])\nfunction getHeaderValueFromOptions({permittedPolicies = \"none\"}) {\n\tif (ALLOWED_PERMITTED_POLICIES.has(permittedPolicies)) {\n\t\treturn permittedPolicies\n\t} else {\n\t\tthrow new Error(`X-Permitted-Cross-Domain-Policies does not support ${JSON.stringify(permittedPolicies)}`)\n\t}\n}\nfunction xPermittedCrossDomainPolicies(options = {}) {\n\tconst headerValue = getHeaderValueFromOptions(options)\n\treturn function xPermittedCrossDomainPoliciesMiddleware(_req, res, next) {\n\t\tres.setHeader(\"X-Permitted-Cross-Domain-Policies\", headerValue)\n\t\tnext()\n\t}\n}\n\nfunction xPoweredBy() {\n\treturn function xPoweredByMiddleware(_req, res, next) {\n\t\tres.removeHeader(\"X-Powered-By\")\n\t\tnext()\n\t}\n}\n\nfunction xXssProtection() {\n\treturn function xXssProtectionMiddleware(_req, res, next) {\n\t\tres.setHeader(\"X-XSS-Protection\", \"0\")\n\t\tnext()\n\t}\n}\n\nfunction getMiddlewareFunctionsFromOptions(options) {\n\tvar _a, _b, _c, _d, _e, _f, _g, _h\n\tconst result = []\n\tswitch (options.contentSecurityPolicy) {\n\t\tcase undefined:\n\t\tcase true:\n\t\t\tresult.push(contentSecurityPolicy())\n\t\t\tbreak\n\t\tcase false:\n\t\t\tbreak\n\t\tdefault:\n\t\t\tresult.push(contentSecurityPolicy(options.contentSecurityPolicy))\n\t\t\tbreak\n\t}\n\tswitch (options.crossOriginEmbedderPolicy) {\n\t\tcase undefined:\n\t\tcase false:\n\t\t\tbreak\n\t\tcase true:\n\t\t\tresult.push(crossOriginEmbedderPolicy())\n\t\t\tbreak\n\t\tdefault:\n\t\t\tresult.push(crossOriginEmbedderPolicy(options.crossOriginEmbedderPolicy))\n\t\t\tbreak\n\t}\n\tswitch (options.crossOriginOpenerPolicy) {\n\t\tcase undefined:\n\t\tcase true:\n\t\t\tresult.push(crossOriginOpenerPolicy())\n\t\t\tbreak\n\t\tcase false:\n\t\t\tbreak\n\t\tdefault:\n\t\t\tresult.push(crossOriginOpenerPolicy(options.crossOriginOpenerPolicy))\n\t\t\tbreak\n\t}\n\tswitch (options.crossOriginResourcePolicy) {\n\t\tcase undefined:\n\t\tcase true:\n\t\t\tresult.push(crossOriginResourcePolicy())\n\t\t\tbreak\n\t\tcase false:\n\t\t\tbreak\n\t\tdefault:\n\t\t\tresult.push(crossOriginResourcePolicy(options.crossOriginResourcePolicy))\n\t\t\tbreak\n\t}\n\tswitch (options.originAgentCluster) {\n\t\tcase undefined:\n\t\tcase true:\n\t\t\tresult.push(originAgentCluster())\n\t\t\tbreak\n\t\tcase false:\n\t\t\tbreak\n\t\tdefault:\n\t\t\tconsole.warn(\"Origin-Agent-Cluster does not take options. Remove the property to silence this warning.\")\n\t\t\tresult.push(originAgentCluster())\n\t\t\tbreak\n\t}\n\tswitch (options.referrerPolicy) {\n\t\tcase undefined:\n\t\tcase true:\n\t\t\tresult.push(referrerPolicy())\n\t\t\tbreak\n\t\tcase false:\n\t\t\tbreak\n\t\tdefault:\n\t\t\tresult.push(referrerPolicy(options.referrerPolicy))\n\t\t\tbreak\n\t}\n\tif (\"strictTransportSecurity\" in options && \"hsts\" in options) {\n\t\tthrow new Error(\"Strict-Transport-Security option was specified twice. Remove `hsts` to silence this warning.\")\n\t}\n\tconst strictTransportSecurityOption = (_a = options.strictTransportSecurity) !== null && _a !== void 0 ? _a : options.hsts\n\tswitch (strictTransportSecurityOption) {\n\t\tcase undefined:\n\t\tcase true:\n\t\t\tresult.push(strictTransportSecurity())\n\t\t\tbreak\n\t\tcase false:\n\t\t\tbreak\n\t\tdefault:\n\t\t\tresult.push(strictTransportSecurity(strictTransportSecurityOption))\n\t\t\tbreak\n\t}\n\tif (\"xContentTypeOptions\" in options && \"noSniff\" in options) {\n\t\tthrow new Error(\"X-Content-Type-Options option was specified twice. Remove `noSniff` to silence this warning.\")\n\t}\n\tconst xContentTypeOptionsOption = (_b = options.xContentTypeOptions) !== null && _b !== void 0 ? _b : options.noSniff\n\tswitch (xContentTypeOptionsOption) {\n\t\tcase undefined:\n\t\tcase true:\n\t\t\tresult.push(xContentTypeOptions())\n\t\t\tbreak\n\t\tcase false:\n\t\t\tbreak\n\t\tdefault:\n\t\t\tconsole.warn(\"X-Content-Type-Options does not take options. Remove the property to silence this warning.\")\n\t\t\tresult.push(xContentTypeOptions())\n\t\t\tbreak\n\t}\n\tif (\"xDnsPrefetchControl\" in options && \"dnsPrefetchControl\" in options) {\n\t\tthrow new Error(\"X-DNS-Prefetch-Control option was specified twice. Remove `dnsPrefetchControl` to silence this warning.\")\n\t}\n\tconst xDnsPrefetchControlOption = (_c = options.xDnsPrefetchControl) !== null && _c !== void 0 ? _c : options.dnsPrefetchControl\n\tswitch (xDnsPrefetchControlOption) {\n\t\tcase undefined:\n\t\tcase true:\n\t\t\tresult.push(xDnsPrefetchControl())\n\t\t\tbreak\n\t\tcase false:\n\t\t\tbreak\n\t\tdefault:\n\t\t\tresult.push(xDnsPrefetchControl(xDnsPrefetchControlOption))\n\t\t\tbreak\n\t}\n\tif (\"xDownloadOptions\" in options && \"ieNoOpen\" in options) {\n\t\tthrow new Error(\"X-Download-Options option was specified twice. Remove `ieNoOpen` to silence this warning.\")\n\t}\n\tconst xDownloadOptionsOption = (_d = options.xDownloadOptions) !== null && _d !== void 0 ? _d : options.ieNoOpen\n\tswitch (xDownloadOptionsOption) {\n\t\tcase undefined:\n\t\tcase true:\n\t\t\tresult.push(xDownloadOptions())\n\t\t\tbreak\n\t\tcase false:\n\t\t\tbreak\n\t\tdefault:\n\t\t\tconsole.warn(\"X-Download-Options does not take options. Remove the property to silence this warning.\")\n\t\t\tresult.push(xDownloadOptions())\n\t\t\tbreak\n\t}\n\tif (\"xFrameOptions\" in options && \"frameguard\" in options) {\n\t\tthrow new Error(\"X-Frame-Options option was specified twice. Remove `frameguard` to silence this warning.\")\n\t}\n\tconst xFrameOptionsOption = (_e = options.xFrameOptions) !== null && _e !== void 0 ? _e : options.frameguard\n\tswitch (xFrameOptionsOption) {\n\t\tcase undefined:\n\t\tcase true:\n\t\t\tresult.push(xFrameOptions())\n\t\t\tbreak\n\t\tcase false:\n\t\t\tbreak\n\t\tdefault:\n\t\t\tresult.push(xFrameOptions(xFrameOptionsOption))\n\t\t\tbreak\n\t}\n\tif (\"xPermittedCrossDomainPolicies\" in options && \"permittedCrossDomainPolicies\" in options) {\n\t\tthrow new Error(\"X-Permitted-Cross-Domain-Policies option was specified twice. Remove `permittedCrossDomainPolicies` to silence this warning.\")\n\t}\n\tconst xPermittedCrossDomainPoliciesOption = (_f = options.xPermittedCrossDomainPolicies) !== null && _f !== void 0 ? _f : options.permittedCrossDomainPolicies\n\tswitch (xPermittedCrossDomainPoliciesOption) {\n\t\tcase undefined:\n\t\tcase true:\n\t\t\tresult.push(xPermittedCrossDomainPolicies())\n\t\t\tbreak\n\t\tcase false:\n\t\t\tbreak\n\t\tdefault:\n\t\t\tresult.push(xPermittedCrossDomainPolicies(xPermittedCrossDomainPoliciesOption))\n\t\t\tbreak\n\t}\n\tif (\"xPoweredBy\" in options && \"hidePoweredBy\" in options) {\n\t\tthrow new Error(\"X-Powered-By option was specified twice. Remove `hidePoweredBy` to silence this warning.\")\n\t}\n\tconst xPoweredByOption = (_g = options.xPoweredBy) !== null && _g !== void 0 ? _g : options.hidePoweredBy\n\tswitch (xPoweredByOption) {\n\t\tcase undefined:\n\t\tcase true:\n\t\t\tresult.push(xPoweredBy())\n\t\t\tbreak\n\t\tcase false:\n\t\t\tbreak\n\t\tdefault:\n\t\t\tconsole.warn(\"X-Powered-By does not take options. Remove the property to silence this warning.\")\n\t\t\tresult.push(xPoweredBy())\n\t\t\tbreak\n\t}\n\tif (\"xXssProtection\" in options && \"xssFilter\" in options) {\n\t\tthrow new Error(\"X-XSS-Protection option was specified twice. Remove `xssFilter` to silence this warning.\")\n\t}\n\tconst xXssProtectionOption = (_h = options.xXssProtection) !== null && _h !== void 0 ? _h : options.xssFilter\n\tswitch (xXssProtectionOption) {\n\t\tcase undefined:\n\t\tcase true:\n\t\t\tresult.push(xXssProtection())\n\t\t\tbreak\n\t\tcase false:\n\t\t\tbreak\n\t\tdefault:\n\t\t\tconsole.warn(\"X-XSS-Protection does not take options. Remove the property to silence this warning.\")\n\t\t\tresult.push(xXssProtection())\n\t\t\tbreak\n\t}\n\treturn result\n}\nconst helmet = Object.assign(\n\tfunction helmet(options = {}) {\n\t\tvar _a\n\t\t// People should be able to pass an options object with no prototype,\n\t\t// so we want this optional chaining.\n\t\t// eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n\t\tif (((_a = options.constructor) === null || _a === void 0 ? void 0 : _a.name) === \"IncomingMessage\") {\n\t\t\tthrow new Error(\"It appears you have done something like `app.use(helmet)`, but it should be `app.use(helmet())`.\")\n\t\t}\n\t\tconst middlewareFunctions = getMiddlewareFunctionsFromOptions(options)\n\t\treturn function helmetMiddleware(req, res, next) {\n\t\t\tlet middlewareIndex = 0\n\t\t\t;(function internalNext(err) {\n\t\t\t\tif (err) {\n\t\t\t\t\tnext(err)\n\t\t\t\t\treturn\n\t\t\t\t}\n\t\t\t\tconst middlewareFunction = middlewareFunctions[middlewareIndex]\n\t\t\t\tif (middlewareFunction) {\n\t\t\t\t\tmiddlewareIndex++\n\t\t\t\t\tmiddlewareFunction(req, res, internalNext)\n\t\t\t\t} else {\n\t\t\t\t\tnext()\n\t\t\t\t}\n\t\t\t})()\n\t\t}\n\t},\n\t{\n\t\tcontentSecurityPolicy,\n\t\tcrossOriginEmbedderPolicy,\n\t\tcrossOriginOpenerPolicy,\n\t\tcrossOriginResourcePolicy,\n\t\toriginAgentCluster,\n\t\treferrerPolicy,\n\t\tstrictTransportSecurity,\n\t\txContentTypeOptions,\n\t\txDnsPrefetchControl,\n\t\txDownloadOptions,\n\t\txFrameOptions,\n\t\txPermittedCrossDomainPolicies,\n\t\txPoweredBy,\n\t\txXssProtection,\n\t\t// Legacy aliases\n\t\tdnsPrefetchControl: xDnsPrefetchControl,\n\t\txssFilter: xXssProtection,\n\t\tpermittedCrossDomainPolicies: xPermittedCrossDomainPolicies,\n\t\tieNoOpen: xDownloadOptions,\n\t\tnoSniff: xContentTypeOptions,\n\t\tframeguard: xFrameOptions,\n\t\thidePoweredBy: xPoweredBy,\n\t\thsts: strictTransportSecurity\n\t}\n)\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/helmet/index.mjs\n");

/***/ })

};
;