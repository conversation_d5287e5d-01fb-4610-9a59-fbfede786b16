/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/concat-stream";
exports.ids = ["vendor-chunks/concat-stream"];
exports.modules = {

/***/ "(rsc)/./node_modules/concat-stream/index.js":
/*!*********************************************!*\
  !*** ./node_modules/concat-stream/index.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var Writable = (__webpack_require__(/*! readable-stream */ \"(rsc)/./node_modules/readable-stream/readable.js\").Writable)\nvar inherits = __webpack_require__(/*! inherits */ \"(rsc)/./node_modules/inherits/inherits.js\")\nvar bufferFrom = __webpack_require__(/*! buffer-from */ \"(rsc)/./node_modules/buffer-from/index.js\")\n\nif (typeof Uint8Array === 'undefined') {\n  var U8 = (__webpack_require__(/*! typedarray */ \"(rsc)/./node_modules/typedarray/index.js\").Uint8Array)\n} else {\n  var U8 = Uint8Array\n}\n\nfunction ConcatStream(opts, cb) {\n  if (!(this instanceof ConcatStream)) return new ConcatStream(opts, cb)\n\n  if (typeof opts === 'function') {\n    cb = opts\n    opts = {}\n  }\n  if (!opts) opts = {}\n\n  var encoding = opts.encoding\n  var shouldInferEncoding = false\n\n  if (!encoding) {\n    shouldInferEncoding = true\n  } else {\n    encoding =  String(encoding).toLowerCase()\n    if (encoding === 'u8' || encoding === 'uint8') {\n      encoding = 'uint8array'\n    }\n  }\n\n  Writable.call(this, { objectMode: true })\n\n  this.encoding = encoding\n  this.shouldInferEncoding = shouldInferEncoding\n\n  if (cb) this.on('finish', function () { cb(this.getBody()) })\n  this.body = []\n}\n\nmodule.exports = ConcatStream\ninherits(ConcatStream, Writable)\n\nConcatStream.prototype._write = function(chunk, enc, next) {\n  this.body.push(chunk)\n  next()\n}\n\nConcatStream.prototype.inferEncoding = function (buff) {\n  var firstBuffer = buff === undefined ? this.body[0] : buff;\n  if (Buffer.isBuffer(firstBuffer)) return 'buffer'\n  if (typeof Uint8Array !== 'undefined' && firstBuffer instanceof Uint8Array) return 'uint8array'\n  if (Array.isArray(firstBuffer)) return 'array'\n  if (typeof firstBuffer === 'string') return 'string'\n  if (Object.prototype.toString.call(firstBuffer) === \"[object Object]\") return 'object'\n  return 'buffer'\n}\n\nConcatStream.prototype.getBody = function () {\n  if (!this.encoding && this.body.length === 0) return []\n  if (this.shouldInferEncoding) this.encoding = this.inferEncoding()\n  if (this.encoding === 'array') return arrayConcat(this.body)\n  if (this.encoding === 'string') return stringConcat(this.body)\n  if (this.encoding === 'buffer') return bufferConcat(this.body)\n  if (this.encoding === 'uint8array') return u8Concat(this.body)\n  return this.body\n}\n\nvar isArray = Array.isArray || function (arr) {\n  return Object.prototype.toString.call(arr) == '[object Array]'\n}\n\nfunction isArrayish (arr) {\n  return /Array\\]$/.test(Object.prototype.toString.call(arr))\n}\n\nfunction isBufferish (p) {\n  return typeof p === 'string' || isArrayish(p) || (p && typeof p.subarray === 'function')\n}\n\nfunction stringConcat (parts) {\n  var strings = []\n  var needsToString = false\n  for (var i = 0; i < parts.length; i++) {\n    var p = parts[i]\n    if (typeof p === 'string') {\n      strings.push(p)\n    } else if (Buffer.isBuffer(p)) {\n      strings.push(p)\n    } else if (isBufferish(p)) {\n      strings.push(bufferFrom(p))\n    } else {\n      strings.push(bufferFrom(String(p)))\n    }\n  }\n  if (Buffer.isBuffer(parts[0])) {\n    strings = Buffer.concat(strings)\n    strings = strings.toString('utf8')\n  } else {\n    strings = strings.join('')\n  }\n  return strings\n}\n\nfunction bufferConcat (parts) {\n  var bufs = []\n  for (var i = 0; i < parts.length; i++) {\n    var p = parts[i]\n    if (Buffer.isBuffer(p)) {\n      bufs.push(p)\n    } else if (isBufferish(p)) {\n      bufs.push(bufferFrom(p))\n    } else {\n      bufs.push(bufferFrom(String(p)))\n    }\n  }\n  return Buffer.concat(bufs)\n}\n\nfunction arrayConcat (parts) {\n  var res = []\n  for (var i = 0; i < parts.length; i++) {\n    res.push.apply(res, parts[i])\n  }\n  return res\n}\n\nfunction u8Concat (parts) {\n  var len = 0\n  for (var i = 0; i < parts.length; i++) {\n    if (typeof parts[i] === 'string') {\n      parts[i] = bufferFrom(parts[i])\n    }\n    len += parts[i].length\n  }\n  var u8 = new U8(len)\n  for (var i = 0, offset = 0; i < parts.length; i++) {\n    var part = parts[i]\n    for (var j = 0; j < part.length; j++) {\n      u8[offset++] = part[j]\n    }\n  }\n  return u8\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/concat-stream/index.js\n");

/***/ })

};
;