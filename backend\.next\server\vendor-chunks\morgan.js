/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/morgan";
exports.ids = ["vendor-chunks/morgan"];
exports.modules = {

/***/ "(rsc)/./node_modules/morgan/index.js":
/*!**************************************!*\
  !*** ./node_modules/morgan/index.js ***!
  \**************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("/*!\n * morgan\n * Copyright(c) 2010 Sencha Inc.\n * Copyright(c) 2011 TJ Holowaychuk\n * Copyright(c) 2014 Jonathan Ong\n * Copyright(c) 2014-2017 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = morgan\nmodule.exports.compile = compile\nmodule.exports.format = format\nmodule.exports.token = token\n\n/**\n * Module dependencies.\n * @private\n */\n\nvar auth = __webpack_require__(/*! basic-auth */ \"(rsc)/./node_modules/basic-auth/index.js\")\nvar debug = __webpack_require__(/*! debug */ \"(rsc)/./node_modules/morgan/node_modules/debug/src/index.js\")('morgan')\nvar deprecate = __webpack_require__(/*! depd */ \"(rsc)/./node_modules/depd/index.js\")('morgan')\nvar onFinished = __webpack_require__(/*! on-finished */ \"(rsc)/./node_modules/morgan/node_modules/on-finished/index.js\")\nvar onHeaders = __webpack_require__(/*! on-headers */ \"(rsc)/./node_modules/on-headers/index.js\")\n\n/**\n * Array of CLF month names.\n * @private\n */\n\nvar CLF_MONTH = [\n  'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',\n  'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'\n]\n\n/**\n * Default log buffer duration.\n * @private\n */\n\nvar DEFAULT_BUFFER_DURATION = 1000\n\n/**\n * Create a logger middleware.\n *\n * @public\n * @param {String|Function} format\n * @param {Object} [options]\n * @return {Function} middleware\n */\n\nfunction morgan (format, options) {\n  var fmt = format\n  var opts = options || {}\n\n  if (format && typeof format === 'object') {\n    opts = format\n    fmt = opts.format || 'default'\n\n    // smart deprecation message\n    deprecate('morgan(options): use morgan(' + (typeof fmt === 'string' ? JSON.stringify(fmt) : 'format') + ', options) instead')\n  }\n\n  if (fmt === undefined) {\n    deprecate('undefined format: specify a format')\n  }\n\n  // output on request instead of response\n  var immediate = opts.immediate\n\n  // check if log entry should be skipped\n  var skip = opts.skip || false\n\n  // format function\n  var formatLine = typeof fmt !== 'function'\n    ? getFormatFunction(fmt)\n    : fmt\n\n  // stream\n  var buffer = opts.buffer\n  var stream = opts.stream || process.stdout\n\n  // buffering support\n  if (buffer) {\n    deprecate('buffer option')\n\n    // flush interval\n    var interval = typeof buffer !== 'number'\n      ? DEFAULT_BUFFER_DURATION\n      : buffer\n\n    // swap the stream\n    stream = createBufferStream(stream, interval)\n  }\n\n  return function logger (req, res, next) {\n    // request data\n    req._startAt = undefined\n    req._startTime = undefined\n    req._remoteAddress = getip(req)\n\n    // response data\n    res._startAt = undefined\n    res._startTime = undefined\n\n    // record request start\n    recordStartTime.call(req)\n\n    function logRequest () {\n      if (skip !== false && skip(req, res)) {\n        debug('skip request')\n        return\n      }\n\n      var line = formatLine(morgan, req, res)\n\n      if (line == null) {\n        debug('skip line')\n        return\n      }\n\n      debug('log request')\n      stream.write(line + '\\n')\n    };\n\n    if (immediate) {\n      // immediate log\n      logRequest()\n    } else {\n      // record response start\n      onHeaders(res, recordStartTime)\n\n      // log when response finished\n      onFinished(res, logRequest)\n    }\n\n    next()\n  }\n}\n\n/**\n * Apache combined log format.\n */\n\nmorgan.format('combined', ':remote-addr - :remote-user [:date[clf]] \":method :url HTTP/:http-version\" :status :res[content-length] \":referrer\" \":user-agent\"')\n\n/**\n * Apache common log format.\n */\n\nmorgan.format('common', ':remote-addr - :remote-user [:date[clf]] \":method :url HTTP/:http-version\" :status :res[content-length]')\n\n/**\n * Default format.\n */\n\nmorgan.format('default', ':remote-addr - :remote-user [:date] \":method :url HTTP/:http-version\" :status :res[content-length] \":referrer\" \":user-agent\"')\ndeprecate.property(morgan, 'default', 'default format: use combined format')\n\n/**\n * Short format.\n */\n\nmorgan.format('short', ':remote-addr :remote-user :method :url HTTP/:http-version :status :res[content-length] - :response-time ms')\n\n/**\n * Tiny format.\n */\n\nmorgan.format('tiny', ':method :url :status :res[content-length] - :response-time ms')\n\n/**\n * dev (colored)\n */\n\nmorgan.format('dev', function developmentFormatLine (tokens, req, res) {\n  // get the status code if response written\n  var status = headersSent(res)\n    ? res.statusCode\n    : undefined\n\n  // get status color\n  var color = status >= 500 ? 31 // red\n    : status >= 400 ? 33 // yellow\n      : status >= 300 ? 36 // cyan\n        : status >= 200 ? 32 // green\n          : 0 // no color\n\n  // get colored function\n  var fn = developmentFormatLine[color]\n\n  if (!fn) {\n    // compile\n    fn = developmentFormatLine[color] = compile('\\x1b[0m:method :url \\x1b[' +\n      color + 'm:status\\x1b[0m :response-time ms - :res[content-length]\\x1b[0m')\n  }\n\n  return fn(tokens, req, res)\n})\n\n/**\n * request url\n */\n\nmorgan.token('url', function getUrlToken (req) {\n  return req.originalUrl || req.url\n})\n\n/**\n * request method\n */\n\nmorgan.token('method', function getMethodToken (req) {\n  return req.method\n})\n\n/**\n * response time in milliseconds\n */\n\nmorgan.token('response-time', function getResponseTimeToken (req, res, digits) {\n  if (!req._startAt || !res._startAt) {\n    // missing request and/or response start time\n    return\n  }\n\n  // calculate diff\n  var ms = (res._startAt[0] - req._startAt[0]) * 1e3 +\n    (res._startAt[1] - req._startAt[1]) * 1e-6\n\n  // return truncated value\n  return ms.toFixed(digits === undefined ? 3 : digits)\n})\n\n/**\n * total time in milliseconds\n */\n\nmorgan.token('total-time', function getTotalTimeToken (req, res, digits) {\n  if (!req._startAt || !res._startAt) {\n    // missing request and/or response start time\n    return\n  }\n\n  // time elapsed from request start\n  var elapsed = process.hrtime(req._startAt)\n\n  // cover to milliseconds\n  var ms = (elapsed[0] * 1e3) + (elapsed[1] * 1e-6)\n\n  // return truncated value\n  return ms.toFixed(digits === undefined ? 3 : digits)\n})\n\n/**\n * current date\n */\n\nmorgan.token('date', function getDateToken (req, res, format) {\n  var date = new Date()\n\n  switch (format || 'web') {\n    case 'clf':\n      return clfdate(date)\n    case 'iso':\n      return date.toISOString()\n    case 'web':\n      return date.toUTCString()\n  }\n})\n\n/**\n * response status code\n */\n\nmorgan.token('status', function getStatusToken (req, res) {\n  return headersSent(res)\n    ? String(res.statusCode)\n    : undefined\n})\n\n/**\n * normalized referrer\n */\n\nmorgan.token('referrer', function getReferrerToken (req) {\n  return req.headers.referer || req.headers.referrer\n})\n\n/**\n * remote address\n */\n\nmorgan.token('remote-addr', getip)\n\n/**\n * remote user\n */\n\nmorgan.token('remote-user', function getRemoteUserToken (req) {\n  // parse basic credentials\n  var credentials = auth(req)\n\n  // return username\n  return credentials\n    ? credentials.name\n    : undefined\n})\n\n/**\n * HTTP version\n */\n\nmorgan.token('http-version', function getHttpVersionToken (req) {\n  return req.httpVersionMajor + '.' + req.httpVersionMinor\n})\n\n/**\n * UA string\n */\n\nmorgan.token('user-agent', function getUserAgentToken (req) {\n  return req.headers['user-agent']\n})\n\n/**\n * request header\n */\n\nmorgan.token('req', function getRequestToken (req, res, field) {\n  // get header\n  var header = req.headers[field.toLowerCase()]\n\n  return Array.isArray(header)\n    ? header.join(', ')\n    : header\n})\n\n/**\n * response header\n */\n\nmorgan.token('res', function getResponseHeader (req, res, field) {\n  if (!headersSent(res)) {\n    return undefined\n  }\n\n  // get header\n  var header = res.getHeader(field)\n\n  return Array.isArray(header)\n    ? header.join(', ')\n    : header\n})\n\n/**\n * Format a Date in the common log format.\n *\n * @private\n * @param {Date} dateTime\n * @return {string}\n */\n\nfunction clfdate (dateTime) {\n  var date = dateTime.getUTCDate()\n  var hour = dateTime.getUTCHours()\n  var mins = dateTime.getUTCMinutes()\n  var secs = dateTime.getUTCSeconds()\n  var year = dateTime.getUTCFullYear()\n\n  var month = CLF_MONTH[dateTime.getUTCMonth()]\n\n  return pad2(date) + '/' + month + '/' + year +\n    ':' + pad2(hour) + ':' + pad2(mins) + ':' + pad2(secs) +\n    ' +0000'\n}\n\n/**\n * Compile a format string into a function.\n *\n * @param {string} format\n * @return {function}\n * @public\n */\n\nfunction compile (format) {\n  if (typeof format !== 'string') {\n    throw new TypeError('argument format must be a string')\n  }\n\n  var fmt = String(JSON.stringify(format))\n  var js = '  \"use strict\"\\n  return ' + fmt.replace(/:([-\\w]{2,})(?:\\[([^\\]]+)\\])?/g, function (_, name, arg) {\n    var tokenArguments = 'req, res'\n    var tokenFunction = 'tokens[' + String(JSON.stringify(name)) + ']'\n\n    if (arg !== undefined) {\n      tokenArguments += ', ' + String(JSON.stringify(arg))\n    }\n\n    return '\" +\\n    (' + tokenFunction + '(' + tokenArguments + ') || \"-\") + \"'\n  })\n\n  // eslint-disable-next-line no-new-func\n  return new Function('tokens, req, res', js)\n}\n\n/**\n * Create a basic buffering stream.\n *\n * @param {object} stream\n * @param {number} interval\n * @public\n */\n\nfunction createBufferStream (stream, interval) {\n  var buf = []\n  var timer = null\n\n  // flush function\n  function flush () {\n    timer = null\n    stream.write(buf.join(''))\n    buf.length = 0\n  }\n\n  // write function\n  function write (str) {\n    if (timer === null) {\n      timer = setTimeout(flush, interval)\n    }\n\n    buf.push(str)\n  }\n\n  // return a minimal \"stream\"\n  return { write: write }\n}\n\n/**\n * Define a format with the given name.\n *\n * @param {string} name\n * @param {string|function} fmt\n * @public\n */\n\nfunction format (name, fmt) {\n  morgan[name] = fmt\n  return this\n}\n\n/**\n * Lookup and compile a named format function.\n *\n * @param {string} name\n * @return {function}\n * @public\n */\n\nfunction getFormatFunction (name) {\n  // lookup format\n  var fmt = morgan[name] || name || morgan.default\n\n  // return compiled format\n  return typeof fmt !== 'function'\n    ? compile(fmt)\n    : fmt\n}\n\n/**\n * Get request IP address.\n *\n * @private\n * @param {IncomingMessage} req\n * @return {string}\n */\n\nfunction getip (req) {\n  return req.ip ||\n    req._remoteAddress ||\n    (req.connection && req.connection.remoteAddress) ||\n    undefined\n}\n\n/**\n * Determine if the response headers have been sent.\n *\n * @param {object} res\n * @returns {boolean}\n * @private\n */\n\nfunction headersSent (res) {\n  // istanbul ignore next: node.js 0.8 support\n  return typeof res.headersSent !== 'boolean'\n    ? Boolean(res._header)\n    : res.headersSent\n}\n\n/**\n * Pad number to two digits.\n *\n * @private\n * @param {number} num\n * @return {string}\n */\n\nfunction pad2 (num) {\n  var str = String(num)\n\n  // istanbul ignore next: num is current datetime\n  return (str.length === 1 ? '0' : '') + str\n}\n\n/**\n * Record the start time.\n * @private\n */\n\nfunction recordStartTime () {\n  this._startAt = process.hrtime()\n  this._startTime = new Date()\n}\n\n/**\n * Define a token function with the given name,\n * and callback fn(req, res).\n *\n * @param {string} name\n * @param {function} fn\n * @public\n */\n\nfunction token (name, fn) {\n  morgan[name] = fn\n  return this\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/morgan/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/morgan/node_modules/debug/src/browser.js":
/*!***************************************************************!*\
  !*** ./node_modules/morgan/node_modules/debug/src/browser.js ***!
  \***************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("/**\n * This is the web browser implementation of `debug()`.\n *\n * Expose `debug()` as the module.\n */\n\nexports = module.exports = __webpack_require__(/*! ./debug */ \"(rsc)/./node_modules/morgan/node_modules/debug/src/debug.js\");\nexports.log = log;\nexports.formatArgs = formatArgs;\nexports.save = save;\nexports.load = load;\nexports.useColors = useColors;\nexports.storage = 'undefined' != typeof chrome\n               && 'undefined' != typeof chrome.storage\n                  ? chrome.storage.local\n                  : localstorage();\n\n/**\n * Colors.\n */\n\nexports.colors = [\n  'lightseagreen',\n  'forestgreen',\n  'goldenrod',\n  'dodgerblue',\n  'darkorchid',\n  'crimson'\n];\n\n/**\n * Currently only WebKit-based Web Inspectors, Firefox >= v31,\n * and the Firebug extension (any Firefox version) are known\n * to support \"%c\" CSS customizations.\n *\n * TODO: add a `localStorage` variable to explicitly enable/disable colors\n */\n\nfunction useColors() {\n  // NB: In an Electron preload script, document will be defined but not fully\n  // initialized. Since we know we're in Chrome, we'll just detect this case\n  // explicitly\n  if (typeof window !== 'undefined' && window.process && window.process.type === 'renderer') {\n    return true;\n  }\n\n  // is webkit? http://stackoverflow.com/a/16459606/376773\n  // document is undefined in react-native: https://github.com/facebook/react-native/pull/1632\n  return (typeof document !== 'undefined' && document.documentElement && document.documentElement.style && document.documentElement.style.WebkitAppearance) ||\n    // is firebug? http://stackoverflow.com/a/398120/376773\n    (typeof window !== 'undefined' && window.console && (window.console.firebug || (window.console.exception && window.console.table))) ||\n    // is firefox >= v31?\n    // https://developer.mozilla.org/en-US/docs/Tools/Web_Console#Styling_messages\n    (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/firefox\\/(\\d+)/) && parseInt(RegExp.$1, 10) >= 31) ||\n    // double check webkit in userAgent just in case we are in a worker\n    (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/applewebkit\\/(\\d+)/));\n}\n\n/**\n * Map %j to `JSON.stringify()`, since no Web Inspectors do that by default.\n */\n\nexports.formatters.j = function(v) {\n  try {\n    return JSON.stringify(v);\n  } catch (err) {\n    return '[UnexpectedJSONParseError]: ' + err.message;\n  }\n};\n\n\n/**\n * Colorize log arguments if enabled.\n *\n * @api public\n */\n\nfunction formatArgs(args) {\n  var useColors = this.useColors;\n\n  args[0] = (useColors ? '%c' : '')\n    + this.namespace\n    + (useColors ? ' %c' : ' ')\n    + args[0]\n    + (useColors ? '%c ' : ' ')\n    + '+' + exports.humanize(this.diff);\n\n  if (!useColors) return;\n\n  var c = 'color: ' + this.color;\n  args.splice(1, 0, c, 'color: inherit')\n\n  // the final \"%c\" is somewhat tricky, because there could be other\n  // arguments passed either before or after the %c, so we need to\n  // figure out the correct index to insert the CSS into\n  var index = 0;\n  var lastC = 0;\n  args[0].replace(/%[a-zA-Z%]/g, function(match) {\n    if ('%%' === match) return;\n    index++;\n    if ('%c' === match) {\n      // we only are interested in the *last* %c\n      // (the user may have provided their own)\n      lastC = index;\n    }\n  });\n\n  args.splice(lastC, 0, c);\n}\n\n/**\n * Invokes `console.log()` when available.\n * No-op when `console.log` is not a \"function\".\n *\n * @api public\n */\n\nfunction log() {\n  // this hackery is required for IE8/9, where\n  // the `console.log` function doesn't have 'apply'\n  return 'object' === typeof console\n    && console.log\n    && Function.prototype.apply.call(console.log, console, arguments);\n}\n\n/**\n * Save `namespaces`.\n *\n * @param {String} namespaces\n * @api private\n */\n\nfunction save(namespaces) {\n  try {\n    if (null == namespaces) {\n      exports.storage.removeItem('debug');\n    } else {\n      exports.storage.debug = namespaces;\n    }\n  } catch(e) {}\n}\n\n/**\n * Load `namespaces`.\n *\n * @return {String} returns the previously persisted debug modes\n * @api private\n */\n\nfunction load() {\n  var r;\n  try {\n    r = exports.storage.debug;\n  } catch(e) {}\n\n  // If debug isn't set in LS, and we're in Electron, try to load $DEBUG\n  if (!r && typeof process !== 'undefined' && 'env' in process) {\n    r = process.env.DEBUG;\n  }\n\n  return r;\n}\n\n/**\n * Enable namespaces listed in `localStorage.debug` initially.\n */\n\nexports.enable(load());\n\n/**\n * Localstorage attempts to return the localstorage.\n *\n * This is necessary because safari throws\n * when a user disables cookies/localstorage\n * and you attempt to access it.\n *\n * @return {LocalStorage}\n * @api private\n */\n\nfunction localstorage() {\n  try {\n    return window.localStorage;\n  } catch (e) {}\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/morgan/node_modules/debug/src/browser.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/morgan/node_modules/debug/src/debug.js":
/*!*************************************************************!*\
  !*** ./node_modules/morgan/node_modules/debug/src/debug.js ***!
  \*************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("\n/**\n * This is the common logic for both the Node.js and web browser\n * implementations of `debug()`.\n *\n * Expose `debug()` as the module.\n */\n\nexports = module.exports = createDebug.debug = createDebug['default'] = createDebug;\nexports.coerce = coerce;\nexports.disable = disable;\nexports.enable = enable;\nexports.enabled = enabled;\nexports.humanize = __webpack_require__(/*! ms */ \"(rsc)/./node_modules/morgan/node_modules/ms/index.js\");\n\n/**\n * The currently active debug mode names, and names to skip.\n */\n\nexports.names = [];\nexports.skips = [];\n\n/**\n * Map of special \"%n\" handling functions, for the debug \"format\" argument.\n *\n * Valid key names are a single, lower or upper-case letter, i.e. \"n\" and \"N\".\n */\n\nexports.formatters = {};\n\n/**\n * Previous log timestamp.\n */\n\nvar prevTime;\n\n/**\n * Select a color.\n * @param {String} namespace\n * @return {Number}\n * @api private\n */\n\nfunction selectColor(namespace) {\n  var hash = 0, i;\n\n  for (i in namespace) {\n    hash  = ((hash << 5) - hash) + namespace.charCodeAt(i);\n    hash |= 0; // Convert to 32bit integer\n  }\n\n  return exports.colors[Math.abs(hash) % exports.colors.length];\n}\n\n/**\n * Create a debugger with the given `namespace`.\n *\n * @param {String} namespace\n * @return {Function}\n * @api public\n */\n\nfunction createDebug(namespace) {\n\n  function debug() {\n    // disabled?\n    if (!debug.enabled) return;\n\n    var self = debug;\n\n    // set `diff` timestamp\n    var curr = +new Date();\n    var ms = curr - (prevTime || curr);\n    self.diff = ms;\n    self.prev = prevTime;\n    self.curr = curr;\n    prevTime = curr;\n\n    // turn the `arguments` into a proper Array\n    var args = new Array(arguments.length);\n    for (var i = 0; i < args.length; i++) {\n      args[i] = arguments[i];\n    }\n\n    args[0] = exports.coerce(args[0]);\n\n    if ('string' !== typeof args[0]) {\n      // anything else let's inspect with %O\n      args.unshift('%O');\n    }\n\n    // apply any `formatters` transformations\n    var index = 0;\n    args[0] = args[0].replace(/%([a-zA-Z%])/g, function(match, format) {\n      // if we encounter an escaped % then don't increase the array index\n      if (match === '%%') return match;\n      index++;\n      var formatter = exports.formatters[format];\n      if ('function' === typeof formatter) {\n        var val = args[index];\n        match = formatter.call(self, val);\n\n        // now we need to remove `args[index]` since it's inlined in the `format`\n        args.splice(index, 1);\n        index--;\n      }\n      return match;\n    });\n\n    // apply env-specific formatting (colors, etc.)\n    exports.formatArgs.call(self, args);\n\n    var logFn = debug.log || exports.log || console.log.bind(console);\n    logFn.apply(self, args);\n  }\n\n  debug.namespace = namespace;\n  debug.enabled = exports.enabled(namespace);\n  debug.useColors = exports.useColors();\n  debug.color = selectColor(namespace);\n\n  // env-specific initialization logic for debug instances\n  if ('function' === typeof exports.init) {\n    exports.init(debug);\n  }\n\n  return debug;\n}\n\n/**\n * Enables a debug mode by namespaces. This can include modes\n * separated by a colon and wildcards.\n *\n * @param {String} namespaces\n * @api public\n */\n\nfunction enable(namespaces) {\n  exports.save(namespaces);\n\n  exports.names = [];\n  exports.skips = [];\n\n  var split = (typeof namespaces === 'string' ? namespaces : '').split(/[\\s,]+/);\n  var len = split.length;\n\n  for (var i = 0; i < len; i++) {\n    if (!split[i]) continue; // ignore empty strings\n    namespaces = split[i].replace(/\\*/g, '.*?');\n    if (namespaces[0] === '-') {\n      exports.skips.push(new RegExp('^' + namespaces.substr(1) + '$'));\n    } else {\n      exports.names.push(new RegExp('^' + namespaces + '$'));\n    }\n  }\n}\n\n/**\n * Disable debug output.\n *\n * @api public\n */\n\nfunction disable() {\n  exports.enable('');\n}\n\n/**\n * Returns true if the given mode name is enabled, false otherwise.\n *\n * @param {String} name\n * @return {Boolean}\n * @api public\n */\n\nfunction enabled(name) {\n  var i, len;\n  for (i = 0, len = exports.skips.length; i < len; i++) {\n    if (exports.skips[i].test(name)) {\n      return false;\n    }\n  }\n  for (i = 0, len = exports.names.length; i < len; i++) {\n    if (exports.names[i].test(name)) {\n      return true;\n    }\n  }\n  return false;\n}\n\n/**\n * Coerce `val`.\n *\n * @param {Mixed} val\n * @return {Mixed}\n * @api private\n */\n\nfunction coerce(val) {\n  if (val instanceof Error) return val.stack || val.message;\n  return val;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/morgan/node_modules/debug/src/debug.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/morgan/node_modules/debug/src/index.js":
/*!*************************************************************!*\
  !*** ./node_modules/morgan/node_modules/debug/src/index.js ***!
  \*************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * Detect Electron renderer process, which is node, but we should\n * treat as a browser.\n */\n\nif (typeof process !== 'undefined' && process.type === 'renderer') {\n  module.exports = __webpack_require__(/*! ./browser.js */ \"(rsc)/./node_modules/morgan/node_modules/debug/src/browser.js\");\n} else {\n  module.exports = __webpack_require__(/*! ./node.js */ \"(rsc)/./node_modules/morgan/node_modules/debug/src/node.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbW9yZ2FuL25vZGVfbW9kdWxlcy9kZWJ1Zy9zcmMvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxFQUFFLHlIQUF3QztBQUMxQyxFQUFFO0FBQ0YsRUFBRSxtSEFBcUM7QUFDdkMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zcnNyLXByb3BlcnR5LW1hbmFnZW1lbnQtYmFja2VuZC8uL25vZGVfbW9kdWxlcy9tb3JnYW4vbm9kZV9tb2R1bGVzL2RlYnVnL3NyYy9pbmRleC5qcz82OTU1Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogRGV0ZWN0IEVsZWN0cm9uIHJlbmRlcmVyIHByb2Nlc3MsIHdoaWNoIGlzIG5vZGUsIGJ1dCB3ZSBzaG91bGRcbiAqIHRyZWF0IGFzIGEgYnJvd3Nlci5cbiAqL1xuXG5pZiAodHlwZW9mIHByb2Nlc3MgIT09ICd1bmRlZmluZWQnICYmIHByb2Nlc3MudHlwZSA9PT0gJ3JlbmRlcmVyJykge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vYnJvd3Nlci5qcycpO1xufSBlbHNlIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL25vZGUuanMnKTtcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/morgan/node_modules/debug/src/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/morgan/node_modules/debug/src/node.js":
/*!************************************************************!*\
  !*** ./node_modules/morgan/node_modules/debug/src/node.js ***!
  \************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("/**\n * Module dependencies.\n */\n\nvar tty = __webpack_require__(/*! tty */ \"tty\");\nvar util = __webpack_require__(/*! util */ \"util\");\n\n/**\n * This is the Node.js implementation of `debug()`.\n *\n * Expose `debug()` as the module.\n */\n\nexports = module.exports = __webpack_require__(/*! ./debug */ \"(rsc)/./node_modules/morgan/node_modules/debug/src/debug.js\");\nexports.init = init;\nexports.log = log;\nexports.formatArgs = formatArgs;\nexports.save = save;\nexports.load = load;\nexports.useColors = useColors;\n\n/**\n * Colors.\n */\n\nexports.colors = [6, 2, 3, 4, 5, 1];\n\n/**\n * Build up the default `inspectOpts` object from the environment variables.\n *\n *   $ DEBUG_COLORS=no DEBUG_DEPTH=10 DEBUG_SHOW_HIDDEN=enabled node script.js\n */\n\nexports.inspectOpts = Object.keys(process.env).filter(function (key) {\n  return /^debug_/i.test(key);\n}).reduce(function (obj, key) {\n  // camel-case\n  var prop = key\n    .substring(6)\n    .toLowerCase()\n    .replace(/_([a-z])/g, function (_, k) { return k.toUpperCase() });\n\n  // coerce string value into JS value\n  var val = process.env[key];\n  if (/^(yes|on|true|enabled)$/i.test(val)) val = true;\n  else if (/^(no|off|false|disabled)$/i.test(val)) val = false;\n  else if (val === 'null') val = null;\n  else val = Number(val);\n\n  obj[prop] = val;\n  return obj;\n}, {});\n\n/**\n * The file descriptor to write the `debug()` calls to.\n * Set the `DEBUG_FD` env variable to override with another value. i.e.:\n *\n *   $ DEBUG_FD=3 node script.js 3>debug.log\n */\n\nvar fd = parseInt(process.env.DEBUG_FD, 10) || 2;\n\nif (1 !== fd && 2 !== fd) {\n  util.deprecate(function(){}, 'except for stderr(2) and stdout(1), any other usage of DEBUG_FD is deprecated. Override debug.log if you want to use a different log function (https://git.io/debug_fd)')()\n}\n\nvar stream = 1 === fd ? process.stdout :\n             2 === fd ? process.stderr :\n             createWritableStdioStream(fd);\n\n/**\n * Is stdout a TTY? Colored output is enabled when `true`.\n */\n\nfunction useColors() {\n  return 'colors' in exports.inspectOpts\n    ? Boolean(exports.inspectOpts.colors)\n    : tty.isatty(fd);\n}\n\n/**\n * Map %o to `util.inspect()`, all on a single line.\n */\n\nexports.formatters.o = function(v) {\n  this.inspectOpts.colors = this.useColors;\n  return util.inspect(v, this.inspectOpts)\n    .split('\\n').map(function(str) {\n      return str.trim()\n    }).join(' ');\n};\n\n/**\n * Map %o to `util.inspect()`, allowing multiple lines if needed.\n */\n\nexports.formatters.O = function(v) {\n  this.inspectOpts.colors = this.useColors;\n  return util.inspect(v, this.inspectOpts);\n};\n\n/**\n * Adds ANSI color escape codes if enabled.\n *\n * @api public\n */\n\nfunction formatArgs(args) {\n  var name = this.namespace;\n  var useColors = this.useColors;\n\n  if (useColors) {\n    var c = this.color;\n    var prefix = '  \\u001b[3' + c + ';1m' + name + ' ' + '\\u001b[0m';\n\n    args[0] = prefix + args[0].split('\\n').join('\\n' + prefix);\n    args.push('\\u001b[3' + c + 'm+' + exports.humanize(this.diff) + '\\u001b[0m');\n  } else {\n    args[0] = new Date().toUTCString()\n      + ' ' + name + ' ' + args[0];\n  }\n}\n\n/**\n * Invokes `util.format()` with the specified arguments and writes to `stream`.\n */\n\nfunction log() {\n  return stream.write(util.format.apply(util, arguments) + '\\n');\n}\n\n/**\n * Save `namespaces`.\n *\n * @param {String} namespaces\n * @api private\n */\n\nfunction save(namespaces) {\n  if (null == namespaces) {\n    // If you set a process.env field to null or undefined, it gets cast to the\n    // string 'null' or 'undefined'. Just delete instead.\n    delete process.env.DEBUG;\n  } else {\n    process.env.DEBUG = namespaces;\n  }\n}\n\n/**\n * Load `namespaces`.\n *\n * @return {String} returns the previously persisted debug modes\n * @api private\n */\n\nfunction load() {\n  return process.env.DEBUG;\n}\n\n/**\n * Copied from `node/src/node.js`.\n *\n * XXX: It's lame that node doesn't expose this API out-of-the-box. It also\n * relies on the undocumented `tty_wrap.guessHandleType()` which is also lame.\n */\n\nfunction createWritableStdioStream (fd) {\n  var stream;\n  var tty_wrap = process.binding('tty_wrap');\n\n  // Note stream._type is used for test-module-load-list.js\n\n  switch (tty_wrap.guessHandleType(fd)) {\n    case 'TTY':\n      stream = new tty.WriteStream(fd);\n      stream._type = 'tty';\n\n      // Hack to have stream not keep the event loop alive.\n      // See https://github.com/joyent/node/issues/1726\n      if (stream._handle && stream._handle.unref) {\n        stream._handle.unref();\n      }\n      break;\n\n    case 'FILE':\n      var fs = __webpack_require__(/*! fs */ \"fs\");\n      stream = new fs.SyncWriteStream(fd, { autoClose: false });\n      stream._type = 'fs';\n      break;\n\n    case 'PIPE':\n    case 'TCP':\n      var net = __webpack_require__(/*! net */ \"net\");\n      stream = new net.Socket({\n        fd: fd,\n        readable: false,\n        writable: true\n      });\n\n      // FIXME Should probably have an option in net.Socket to create a\n      // stream from an existing fd which is writable only. But for now\n      // we'll just add this hack and set the `readable` member to false.\n      // Test: ./node test/fixtures/echo.js < /etc/passwd\n      stream.readable = false;\n      stream.read = null;\n      stream._type = 'pipe';\n\n      // FIXME Hack to have stream not keep the event loop alive.\n      // See https://github.com/joyent/node/issues/1726\n      if (stream._handle && stream._handle.unref) {\n        stream._handle.unref();\n      }\n      break;\n\n    default:\n      // Probably an error on in uv_guess_handle()\n      throw new Error('Implement me. Unknown stream file type!');\n  }\n\n  // For supporting legacy API we put the FD here.\n  stream.fd = fd;\n\n  stream._isStdio = true;\n\n  return stream;\n}\n\n/**\n * Init logic for `debug` instances.\n *\n * Create a new `inspectOpts` object in case `useColors` is set\n * differently for a particular `debug` instance.\n */\n\nfunction init (debug) {\n  debug.inspectOpts = {};\n\n  var keys = Object.keys(exports.inspectOpts);\n  for (var i = 0; i < keys.length; i++) {\n    debug.inspectOpts[keys[i]] = exports.inspectOpts[keys[i]];\n  }\n}\n\n/**\n * Enable namespaces listed in `process.env.DEBUG` initially.\n */\n\nexports.enable(load());\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/morgan/node_modules/debug/src/node.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/morgan/node_modules/ms/index.js":
/*!******************************************************!*\
  !*** ./node_modules/morgan/node_modules/ms/index.js ***!
  \******************************************************/
/***/ ((module) => {

eval("/**\n * Helpers.\n */\n\nvar s = 1000;\nvar m = s * 60;\nvar h = m * 60;\nvar d = h * 24;\nvar y = d * 365.25;\n\n/**\n * Parse or format the given `val`.\n *\n * Options:\n *\n *  - `long` verbose formatting [false]\n *\n * @param {String|Number} val\n * @param {Object} [options]\n * @throws {Error} throw an error if val is not a non-empty string or a number\n * @return {String|Number}\n * @api public\n */\n\nmodule.exports = function(val, options) {\n  options = options || {};\n  var type = typeof val;\n  if (type === 'string' && val.length > 0) {\n    return parse(val);\n  } else if (type === 'number' && isNaN(val) === false) {\n    return options.long ? fmtLong(val) : fmtShort(val);\n  }\n  throw new Error(\n    'val is not a non-empty string or a valid number. val=' +\n      JSON.stringify(val)\n  );\n};\n\n/**\n * Parse the given `str` and return milliseconds.\n *\n * @param {String} str\n * @return {Number}\n * @api private\n */\n\nfunction parse(str) {\n  str = String(str);\n  if (str.length > 100) {\n    return;\n  }\n  var match = /^((?:\\d+)?\\.?\\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|years?|yrs?|y)?$/i.exec(\n    str\n  );\n  if (!match) {\n    return;\n  }\n  var n = parseFloat(match[1]);\n  var type = (match[2] || 'ms').toLowerCase();\n  switch (type) {\n    case 'years':\n    case 'year':\n    case 'yrs':\n    case 'yr':\n    case 'y':\n      return n * y;\n    case 'days':\n    case 'day':\n    case 'd':\n      return n * d;\n    case 'hours':\n    case 'hour':\n    case 'hrs':\n    case 'hr':\n    case 'h':\n      return n * h;\n    case 'minutes':\n    case 'minute':\n    case 'mins':\n    case 'min':\n    case 'm':\n      return n * m;\n    case 'seconds':\n    case 'second':\n    case 'secs':\n    case 'sec':\n    case 's':\n      return n * s;\n    case 'milliseconds':\n    case 'millisecond':\n    case 'msecs':\n    case 'msec':\n    case 'ms':\n      return n;\n    default:\n      return undefined;\n  }\n}\n\n/**\n * Short format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtShort(ms) {\n  if (ms >= d) {\n    return Math.round(ms / d) + 'd';\n  }\n  if (ms >= h) {\n    return Math.round(ms / h) + 'h';\n  }\n  if (ms >= m) {\n    return Math.round(ms / m) + 'm';\n  }\n  if (ms >= s) {\n    return Math.round(ms / s) + 's';\n  }\n  return ms + 'ms';\n}\n\n/**\n * Long format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtLong(ms) {\n  return plural(ms, d, 'day') ||\n    plural(ms, h, 'hour') ||\n    plural(ms, m, 'minute') ||\n    plural(ms, s, 'second') ||\n    ms + ' ms';\n}\n\n/**\n * Pluralization helper.\n */\n\nfunction plural(ms, n, name) {\n  if (ms < n) {\n    return;\n  }\n  if (ms < n * 1.5) {\n    return Math.floor(ms / n) + ' ' + name;\n  }\n  return Math.ceil(ms / n) + ' ' + name + 's';\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/morgan/node_modules/ms/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/morgan/node_modules/on-finished/index.js":
/*!***************************************************************!*\
  !*** ./node_modules/morgan/node_modules/on-finished/index.js ***!
  \***************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("/*!\n * on-finished\n * Copyright(c) 2013 Jonathan Ong\n * Copyright(c) 2014 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = onFinished\nmodule.exports.isFinished = isFinished\n\n/**\n * Module dependencies.\n * @private\n */\n\nvar first = __webpack_require__(/*! ee-first */ \"(rsc)/./node_modules/ee-first/index.js\")\n\n/**\n * Variables.\n * @private\n */\n\n/* istanbul ignore next */\nvar defer = typeof setImmediate === 'function'\n  ? setImmediate\n  : function(fn){ process.nextTick(fn.bind.apply(fn, arguments)) }\n\n/**\n * Invoke callback when the response has finished, useful for\n * cleaning up resources afterwards.\n *\n * @param {object} msg\n * @param {function} listener\n * @return {object}\n * @public\n */\n\nfunction onFinished(msg, listener) {\n  if (isFinished(msg) !== false) {\n    defer(listener, null, msg)\n    return msg\n  }\n\n  // attach the listener to the message\n  attachListener(msg, listener)\n\n  return msg\n}\n\n/**\n * Determine if message is already finished.\n *\n * @param {object} msg\n * @return {boolean}\n * @public\n */\n\nfunction isFinished(msg) {\n  var socket = msg.socket\n\n  if (typeof msg.finished === 'boolean') {\n    // OutgoingMessage\n    return Boolean(msg.finished || (socket && !socket.writable))\n  }\n\n  if (typeof msg.complete === 'boolean') {\n    // IncomingMessage\n    return Boolean(msg.upgrade || !socket || !socket.readable || (msg.complete && !msg.readable))\n  }\n\n  // don't know\n  return undefined\n}\n\n/**\n * Attach a finished listener to the message.\n *\n * @param {object} msg\n * @param {function} callback\n * @private\n */\n\nfunction attachFinishedListener(msg, callback) {\n  var eeMsg\n  var eeSocket\n  var finished = false\n\n  function onFinish(error) {\n    eeMsg.cancel()\n    eeSocket.cancel()\n\n    finished = true\n    callback(error)\n  }\n\n  // finished on first message event\n  eeMsg = eeSocket = first([[msg, 'end', 'finish']], onFinish)\n\n  function onSocket(socket) {\n    // remove listener\n    msg.removeListener('socket', onSocket)\n\n    if (finished) return\n    if (eeMsg !== eeSocket) return\n\n    // finished on first socket event\n    eeSocket = first([[socket, 'error', 'close']], onFinish)\n  }\n\n  if (msg.socket) {\n    // socket already assigned\n    onSocket(msg.socket)\n    return\n  }\n\n  // wait for socket to be assigned\n  msg.on('socket', onSocket)\n\n  if (msg.socket === undefined) {\n    // node.js 0.8 patch\n    patchAssignSocket(msg, onSocket)\n  }\n}\n\n/**\n * Attach the listener to the message.\n *\n * @param {object} msg\n * @return {function}\n * @private\n */\n\nfunction attachListener(msg, listener) {\n  var attached = msg.__onFinished\n\n  // create a private single listener with queue\n  if (!attached || !attached.queue) {\n    attached = msg.__onFinished = createListener(msg)\n    attachFinishedListener(msg, attached)\n  }\n\n  attached.queue.push(listener)\n}\n\n/**\n * Create listener on message.\n *\n * @param {object} msg\n * @return {function}\n * @private\n */\n\nfunction createListener(msg) {\n  function listener(err) {\n    if (msg.__onFinished === listener) msg.__onFinished = null\n    if (!listener.queue) return\n\n    var queue = listener.queue\n    listener.queue = null\n\n    for (var i = 0; i < queue.length; i++) {\n      queue[i](err, msg)\n    }\n  }\n\n  listener.queue = []\n\n  return listener\n}\n\n/**\n * Patch ServerResponse.prototype.assignSocket for node.js 0.8.\n *\n * @param {ServerResponse} res\n * @param {function} callback\n * @private\n */\n\nfunction patchAssignSocket(res, callback) {\n  var assignSocket = res.assignSocket\n\n  if (typeof assignSocket !== 'function') return\n\n  // res.on('socket', callback) is broken in 0.8\n  res.assignSocket = function _assignSocket(socket) {\n    assignSocket.call(this, socket)\n    callback(socket)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/morgan/node_modules/on-finished/index.js\n");

/***/ })

};
;