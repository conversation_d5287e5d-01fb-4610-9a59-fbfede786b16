/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/depd";
exports.ids = ["vendor-chunks/depd"];
exports.modules = {

/***/ "(rsc)/./node_modules/depd/index.js":
/*!************************************!*\
  !*** ./node_modules/depd/index.js ***!
  \************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/*!\n * depd\n * Copyright(c) 2014-2018 <PERSON>\n * MIT Licensed\n */\n\n/**\n * Module dependencies.\n */\n\nvar relative = (__webpack_require__(/*! path */ \"path\").relative)\n\n/**\n * Module exports.\n */\n\nmodule.exports = depd\n\n/**\n * Get the path to base files on.\n */\n\nvar basePath = process.cwd()\n\n/**\n * Determine if namespace is contained in the string.\n */\n\nfunction containsNamespace (str, namespace) {\n  var vals = str.split(/[ ,]+/)\n  var ns = String(namespace).toLowerCase()\n\n  for (var i = 0; i < vals.length; i++) {\n    var val = vals[i]\n\n    // namespace contained\n    if (val && (val === '*' || val.toLowerCase() === ns)) {\n      return true\n    }\n  }\n\n  return false\n}\n\n/**\n * Convert a data descriptor to accessor descriptor.\n */\n\nfunction convertDataDescriptorToAccessor (obj, prop, message) {\n  var descriptor = Object.getOwnPropertyDescriptor(obj, prop)\n  var value = descriptor.value\n\n  descriptor.get = function getter () { return value }\n\n  if (descriptor.writable) {\n    descriptor.set = function setter (val) { return (value = val) }\n  }\n\n  delete descriptor.value\n  delete descriptor.writable\n\n  Object.defineProperty(obj, prop, descriptor)\n\n  return descriptor\n}\n\n/**\n * Create arguments string to keep arity.\n */\n\nfunction createArgumentsString (arity) {\n  var str = ''\n\n  for (var i = 0; i < arity; i++) {\n    str += ', arg' + i\n  }\n\n  return str.substr(2)\n}\n\n/**\n * Create stack string from stack.\n */\n\nfunction createStackString (stack) {\n  var str = this.name + ': ' + this.namespace\n\n  if (this.message) {\n    str += ' deprecated ' + this.message\n  }\n\n  for (var i = 0; i < stack.length; i++) {\n    str += '\\n    at ' + stack[i].toString()\n  }\n\n  return str\n}\n\n/**\n * Create deprecate for namespace in caller.\n */\n\nfunction depd (namespace) {\n  if (!namespace) {\n    throw new TypeError('argument namespace is required')\n  }\n\n  var stack = getStack()\n  var site = callSiteLocation(stack[1])\n  var file = site[0]\n\n  function deprecate (message) {\n    // call to self as log\n    log.call(deprecate, message)\n  }\n\n  deprecate._file = file\n  deprecate._ignored = isignored(namespace)\n  deprecate._namespace = namespace\n  deprecate._traced = istraced(namespace)\n  deprecate._warned = Object.create(null)\n\n  deprecate.function = wrapfunction\n  deprecate.property = wrapproperty\n\n  return deprecate\n}\n\n/**\n * Determine if event emitter has listeners of a given type.\n *\n * The way to do this check is done three different ways in Node.js >= 0.8\n * so this consolidates them into a minimal set using instance methods.\n *\n * @param {EventEmitter} emitter\n * @param {string} type\n * @returns {boolean}\n * @private\n */\n\nfunction eehaslisteners (emitter, type) {\n  var count = typeof emitter.listenerCount !== 'function'\n    ? emitter.listeners(type).length\n    : emitter.listenerCount(type)\n\n  return count > 0\n}\n\n/**\n * Determine if namespace is ignored.\n */\n\nfunction isignored (namespace) {\n  if (process.noDeprecation) {\n    // --no-deprecation support\n    return true\n  }\n\n  var str = process.env.NO_DEPRECATION || ''\n\n  // namespace ignored\n  return containsNamespace(str, namespace)\n}\n\n/**\n * Determine if namespace is traced.\n */\n\nfunction istraced (namespace) {\n  if (process.traceDeprecation) {\n    // --trace-deprecation support\n    return true\n  }\n\n  var str = process.env.TRACE_DEPRECATION || ''\n\n  // namespace traced\n  return containsNamespace(str, namespace)\n}\n\n/**\n * Display deprecation message.\n */\n\nfunction log (message, site) {\n  var haslisteners = eehaslisteners(process, 'deprecation')\n\n  // abort early if no destination\n  if (!haslisteners && this._ignored) {\n    return\n  }\n\n  var caller\n  var callFile\n  var callSite\n  var depSite\n  var i = 0\n  var seen = false\n  var stack = getStack()\n  var file = this._file\n\n  if (site) {\n    // provided site\n    depSite = site\n    callSite = callSiteLocation(stack[1])\n    callSite.name = depSite.name\n    file = callSite[0]\n  } else {\n    // get call site\n    i = 2\n    depSite = callSiteLocation(stack[i])\n    callSite = depSite\n  }\n\n  // get caller of deprecated thing in relation to file\n  for (; i < stack.length; i++) {\n    caller = callSiteLocation(stack[i])\n    callFile = caller[0]\n\n    if (callFile === file) {\n      seen = true\n    } else if (callFile === this._file) {\n      file = this._file\n    } else if (seen) {\n      break\n    }\n  }\n\n  var key = caller\n    ? depSite.join(':') + '__' + caller.join(':')\n    : undefined\n\n  if (key !== undefined && key in this._warned) {\n    // already warned\n    return\n  }\n\n  this._warned[key] = true\n\n  // generate automatic message from call site\n  var msg = message\n  if (!msg) {\n    msg = callSite === depSite || !callSite.name\n      ? defaultMessage(depSite)\n      : defaultMessage(callSite)\n  }\n\n  // emit deprecation if listeners exist\n  if (haslisteners) {\n    var err = DeprecationError(this._namespace, msg, stack.slice(i))\n    process.emit('deprecation', err)\n    return\n  }\n\n  // format and write message\n  var format = process.stderr.isTTY\n    ? formatColor\n    : formatPlain\n  var output = format.call(this, msg, caller, stack.slice(i))\n  process.stderr.write(output + '\\n', 'utf8')\n}\n\n/**\n * Get call site location as array.\n */\n\nfunction callSiteLocation (callSite) {\n  var file = callSite.getFileName() || '<anonymous>'\n  var line = callSite.getLineNumber()\n  var colm = callSite.getColumnNumber()\n\n  if (callSite.isEval()) {\n    file = callSite.getEvalOrigin() + ', ' + file\n  }\n\n  var site = [file, line, colm]\n\n  site.callSite = callSite\n  site.name = callSite.getFunctionName()\n\n  return site\n}\n\n/**\n * Generate a default message from the site.\n */\n\nfunction defaultMessage (site) {\n  var callSite = site.callSite\n  var funcName = site.name\n\n  // make useful anonymous name\n  if (!funcName) {\n    funcName = '<anonymous@' + formatLocation(site) + '>'\n  }\n\n  var context = callSite.getThis()\n  var typeName = context && callSite.getTypeName()\n\n  // ignore useless type name\n  if (typeName === 'Object') {\n    typeName = undefined\n  }\n\n  // make useful type name\n  if (typeName === 'Function') {\n    typeName = context.name || typeName\n  }\n\n  return typeName && callSite.getMethodName()\n    ? typeName + '.' + funcName\n    : funcName\n}\n\n/**\n * Format deprecation message without color.\n */\n\nfunction formatPlain (msg, caller, stack) {\n  var timestamp = new Date().toUTCString()\n\n  var formatted = timestamp +\n    ' ' + this._namespace +\n    ' deprecated ' + msg\n\n  // add stack trace\n  if (this._traced) {\n    for (var i = 0; i < stack.length; i++) {\n      formatted += '\\n    at ' + stack[i].toString()\n    }\n\n    return formatted\n  }\n\n  if (caller) {\n    formatted += ' at ' + formatLocation(caller)\n  }\n\n  return formatted\n}\n\n/**\n * Format deprecation message with color.\n */\n\nfunction formatColor (msg, caller, stack) {\n  var formatted = '\\x1b[36;1m' + this._namespace + '\\x1b[22;39m' + // bold cyan\n    ' \\x1b[33;1mdeprecated\\x1b[22;39m' + // bold yellow\n    ' \\x1b[0m' + msg + '\\x1b[39m' // reset\n\n  // add stack trace\n  if (this._traced) {\n    for (var i = 0; i < stack.length; i++) {\n      formatted += '\\n    \\x1b[36mat ' + stack[i].toString() + '\\x1b[39m' // cyan\n    }\n\n    return formatted\n  }\n\n  if (caller) {\n    formatted += ' \\x1b[36m' + formatLocation(caller) + '\\x1b[39m' // cyan\n  }\n\n  return formatted\n}\n\n/**\n * Format call site location.\n */\n\nfunction formatLocation (callSite) {\n  return relative(basePath, callSite[0]) +\n    ':' + callSite[1] +\n    ':' + callSite[2]\n}\n\n/**\n * Get the stack as array of call sites.\n */\n\nfunction getStack () {\n  var limit = Error.stackTraceLimit\n  var obj = {}\n  var prep = Error.prepareStackTrace\n\n  Error.prepareStackTrace = prepareObjectStackTrace\n  Error.stackTraceLimit = Math.max(10, limit)\n\n  // capture the stack\n  Error.captureStackTrace(obj)\n\n  // slice this function off the top\n  var stack = obj.stack.slice(1)\n\n  Error.prepareStackTrace = prep\n  Error.stackTraceLimit = limit\n\n  return stack\n}\n\n/**\n * Capture call site stack from v8.\n */\n\nfunction prepareObjectStackTrace (obj, stack) {\n  return stack\n}\n\n/**\n * Return a wrapped function in a deprecation message.\n */\n\nfunction wrapfunction (fn, message) {\n  if (typeof fn !== 'function') {\n    throw new TypeError('argument fn must be a function')\n  }\n\n  var args = createArgumentsString(fn.length)\n  var stack = getStack()\n  var site = callSiteLocation(stack[1])\n\n  site.name = fn.name\n\n  // eslint-disable-next-line no-new-func\n  var deprecatedfn = new Function('fn', 'log', 'deprecate', 'message', 'site',\n    '\"use strict\"\\n' +\n    'return function (' + args + ') {' +\n    'log.call(deprecate, message, site)\\n' +\n    'return fn.apply(this, arguments)\\n' +\n    '}')(fn, log, this, message, site)\n\n  return deprecatedfn\n}\n\n/**\n * Wrap property in a deprecation message.\n */\n\nfunction wrapproperty (obj, prop, message) {\n  if (!obj || (typeof obj !== 'object' && typeof obj !== 'function')) {\n    throw new TypeError('argument obj must be object')\n  }\n\n  var descriptor = Object.getOwnPropertyDescriptor(obj, prop)\n\n  if (!descriptor) {\n    throw new TypeError('must call property on owner object')\n  }\n\n  if (!descriptor.configurable) {\n    throw new TypeError('property must be configurable')\n  }\n\n  var deprecate = this\n  var stack = getStack()\n  var site = callSiteLocation(stack[1])\n\n  // set site name\n  site.name = prop\n\n  // convert data descriptor\n  if ('value' in descriptor) {\n    descriptor = convertDataDescriptorToAccessor(obj, prop, message)\n  }\n\n  var get = descriptor.get\n  var set = descriptor.set\n\n  // wrap getter\n  if (typeof get === 'function') {\n    descriptor.get = function getter () {\n      log.call(deprecate, message, site)\n      return get.apply(this, arguments)\n    }\n  }\n\n  // wrap setter\n  if (typeof set === 'function') {\n    descriptor.set = function setter () {\n      log.call(deprecate, message, site)\n      return set.apply(this, arguments)\n    }\n  }\n\n  Object.defineProperty(obj, prop, descriptor)\n}\n\n/**\n * Create DeprecationError for deprecation\n */\n\nfunction DeprecationError (namespace, message, stack) {\n  var error = new Error()\n  var stackString\n\n  Object.defineProperty(error, 'constructor', {\n    value: DeprecationError\n  })\n\n  Object.defineProperty(error, 'message', {\n    configurable: true,\n    enumerable: false,\n    value: message,\n    writable: true\n  })\n\n  Object.defineProperty(error, 'name', {\n    enumerable: false,\n    configurable: true,\n    value: 'DeprecationError',\n    writable: true\n  })\n\n  Object.defineProperty(error, 'namespace', {\n    configurable: true,\n    enumerable: false,\n    value: namespace,\n    writable: true\n  })\n\n  Object.defineProperty(error, 'stack', {\n    configurable: true,\n    enumerable: false,\n    get: function () {\n      if (stackString !== undefined) {\n        return stackString\n      }\n\n      // prepare stack trace\n      return (stackString = createStackString.call(this, stack))\n    },\n    set: function setter (val) {\n      stackString = val\n    }\n  })\n\n  return error\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/depd/index.js\n");

/***/ })

};
;