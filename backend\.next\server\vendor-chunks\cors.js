/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/cors";
exports.ids = ["vendor-chunks/cors"];
exports.modules = {

/***/ "(rsc)/./node_modules/cors/lib/index.js":
/*!****************************************!*\
  !*** ./node_modules/cors/lib/index.js ***!
  \****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("(function () {\n\n  'use strict';\n\n  var assign = __webpack_require__(/*! object-assign */ \"(rsc)/./node_modules/object-assign/index.js\");\n  var vary = __webpack_require__(/*! vary */ \"(rsc)/./node_modules/vary/index.js\");\n\n  var defaults = {\n    origin: '*',\n    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',\n    preflightContinue: false,\n    optionsSuccessStatus: 204\n  };\n\n  function isString(s) {\n    return typeof s === 'string' || s instanceof String;\n  }\n\n  function isOriginAllowed(origin, allowedOrigin) {\n    if (Array.isArray(allowedOrigin)) {\n      for (var i = 0; i < allowedOrigin.length; ++i) {\n        if (isOriginAllowed(origin, allowedOrigin[i])) {\n          return true;\n        }\n      }\n      return false;\n    } else if (isString(allowedOrigin)) {\n      return origin === allowedOrigin;\n    } else if (allowedOrigin instanceof RegExp) {\n      return allowedOrigin.test(origin);\n    } else {\n      return !!allowedOrigin;\n    }\n  }\n\n  function configureOrigin(options, req) {\n    var requestOrigin = req.headers.origin,\n      headers = [],\n      isAllowed;\n\n    if (!options.origin || options.origin === '*') {\n      // allow any origin\n      headers.push([{\n        key: 'Access-Control-Allow-Origin',\n        value: '*'\n      }]);\n    } else if (isString(options.origin)) {\n      // fixed origin\n      headers.push([{\n        key: 'Access-Control-Allow-Origin',\n        value: options.origin\n      }]);\n      headers.push([{\n        key: 'Vary',\n        value: 'Origin'\n      }]);\n    } else {\n      isAllowed = isOriginAllowed(requestOrigin, options.origin);\n      // reflect origin\n      headers.push([{\n        key: 'Access-Control-Allow-Origin',\n        value: isAllowed ? requestOrigin : false\n      }]);\n      headers.push([{\n        key: 'Vary',\n        value: 'Origin'\n      }]);\n    }\n\n    return headers;\n  }\n\n  function configureMethods(options) {\n    var methods = options.methods;\n    if (methods.join) {\n      methods = options.methods.join(','); // .methods is an array, so turn it into a string\n    }\n    return {\n      key: 'Access-Control-Allow-Methods',\n      value: methods\n    };\n  }\n\n  function configureCredentials(options) {\n    if (options.credentials === true) {\n      return {\n        key: 'Access-Control-Allow-Credentials',\n        value: 'true'\n      };\n    }\n    return null;\n  }\n\n  function configureAllowedHeaders(options, req) {\n    var allowedHeaders = options.allowedHeaders || options.headers;\n    var headers = [];\n\n    if (!allowedHeaders) {\n      allowedHeaders = req.headers['access-control-request-headers']; // .headers wasn't specified, so reflect the request headers\n      headers.push([{\n        key: 'Vary',\n        value: 'Access-Control-Request-Headers'\n      }]);\n    } else if (allowedHeaders.join) {\n      allowedHeaders = allowedHeaders.join(','); // .headers is an array, so turn it into a string\n    }\n    if (allowedHeaders && allowedHeaders.length) {\n      headers.push([{\n        key: 'Access-Control-Allow-Headers',\n        value: allowedHeaders\n      }]);\n    }\n\n    return headers;\n  }\n\n  function configureExposedHeaders(options) {\n    var headers = options.exposedHeaders;\n    if (!headers) {\n      return null;\n    } else if (headers.join) {\n      headers = headers.join(','); // .headers is an array, so turn it into a string\n    }\n    if (headers && headers.length) {\n      return {\n        key: 'Access-Control-Expose-Headers',\n        value: headers\n      };\n    }\n    return null;\n  }\n\n  function configureMaxAge(options) {\n    var maxAge = (typeof options.maxAge === 'number' || options.maxAge) && options.maxAge.toString()\n    if (maxAge && maxAge.length) {\n      return {\n        key: 'Access-Control-Max-Age',\n        value: maxAge\n      };\n    }\n    return null;\n  }\n\n  function applyHeaders(headers, res) {\n    for (var i = 0, n = headers.length; i < n; i++) {\n      var header = headers[i];\n      if (header) {\n        if (Array.isArray(header)) {\n          applyHeaders(header, res);\n        } else if (header.key === 'Vary' && header.value) {\n          vary(res, header.value);\n        } else if (header.value) {\n          res.setHeader(header.key, header.value);\n        }\n      }\n    }\n  }\n\n  function cors(options, req, res, next) {\n    var headers = [],\n      method = req.method && req.method.toUpperCase && req.method.toUpperCase();\n\n    if (method === 'OPTIONS') {\n      // preflight\n      headers.push(configureOrigin(options, req));\n      headers.push(configureCredentials(options, req));\n      headers.push(configureMethods(options, req));\n      headers.push(configureAllowedHeaders(options, req));\n      headers.push(configureMaxAge(options, req));\n      headers.push(configureExposedHeaders(options, req));\n      applyHeaders(headers, res);\n\n      if (options.preflightContinue) {\n        next();\n      } else {\n        // Safari (and potentially other browsers) need content-length 0,\n        //   for 204 or they just hang waiting for a body\n        res.statusCode = options.optionsSuccessStatus;\n        res.setHeader('Content-Length', '0');\n        res.end();\n      }\n    } else {\n      // actual response\n      headers.push(configureOrigin(options, req));\n      headers.push(configureCredentials(options, req));\n      headers.push(configureExposedHeaders(options, req));\n      applyHeaders(headers, res);\n      next();\n    }\n  }\n\n  function middlewareWrapper(o) {\n    // if options are static (either via defaults or custom options passed in), wrap in a function\n    var optionsCallback = null;\n    if (typeof o === 'function') {\n      optionsCallback = o;\n    } else {\n      optionsCallback = function (req, cb) {\n        cb(null, o);\n      };\n    }\n\n    return function corsMiddleware(req, res, next) {\n      optionsCallback(req, function (err, options) {\n        if (err) {\n          next(err);\n        } else {\n          var corsOptions = assign({}, defaults, options);\n          var originCallback = null;\n          if (corsOptions.origin && typeof corsOptions.origin === 'function') {\n            originCallback = corsOptions.origin;\n          } else if (corsOptions.origin) {\n            originCallback = function (origin, cb) {\n              cb(null, corsOptions.origin);\n            };\n          }\n\n          if (originCallback) {\n            originCallback(req.headers.origin, function (err2, origin) {\n              if (err2 || !origin) {\n                next(err2);\n              } else {\n                corsOptions.origin = origin;\n                cors(corsOptions, req, res, next);\n              }\n            });\n          } else {\n            next();\n          }\n        }\n      });\n    };\n  }\n\n  // can pass either an options hash, an options delegate, or nothing\n  module.exports = middlewareWrapper;\n\n}());\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/cors/lib/index.js\n");

/***/ })

};
;