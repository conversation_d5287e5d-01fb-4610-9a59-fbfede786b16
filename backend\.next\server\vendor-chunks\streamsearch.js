"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/streamsearch";
exports.ids = ["vendor-chunks/streamsearch"];
exports.modules = {

/***/ "(rsc)/./node_modules/streamsearch/lib/sbmh.js":
/*!***********************************************!*\
  !*** ./node_modules/streamsearch/lib/sbmh.js ***!
  \***********************************************/
/***/ ((module) => {

eval("\n/*\n  Based heavily on the Streaming Boyer-Moore-Horspool C++ implementation\n  by Hongli Lai at: https://github.com/FooBarWidget/boyer-moore-horspool\n*/\nfunction memcmp(buf1, pos1, buf2, pos2, num) {\n  for (let i = 0; i < num; ++i) {\n    if (buf1[pos1 + i] !== buf2[pos2 + i])\n      return false;\n  }\n  return true;\n}\n\nclass SBMH {\n  constructor(needle, cb) {\n    if (typeof cb !== 'function')\n      throw new Error('Missing match callback');\n\n    if (typeof needle === 'string')\n      needle = Buffer.from(needle);\n    else if (!Buffer.isBuffer(needle))\n      throw new Error(`Expected Buffer for needle, got ${typeof needle}`);\n\n    const needleLen = needle.length;\n\n    this.maxMatches = Infinity;\n    this.matches = 0;\n\n    this._cb = cb;\n    this._lookbehindSize = 0;\n    this._needle = needle;\n    this._bufPos = 0;\n\n    this._lookbehind = Buffer.allocUnsafe(needleLen);\n\n    // Initialize occurrence table.\n    this._occ = [\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen, needleLen, needleLen,\n      needleLen, needleLen, needleLen, needleLen\n    ];\n\n    // Populate occurrence table with analysis of the needle, ignoring the last\n    // letter.\n    if (needleLen > 1) {\n      for (let i = 0; i < needleLen - 1; ++i)\n        this._occ[needle[i]] = needleLen - 1 - i;\n    }\n  }\n\n  reset() {\n    this.matches = 0;\n    this._lookbehindSize = 0;\n    this._bufPos = 0;\n  }\n\n  push(chunk, pos) {\n    let result;\n    if (!Buffer.isBuffer(chunk))\n      chunk = Buffer.from(chunk, 'latin1');\n    const chunkLen = chunk.length;\n    this._bufPos = pos || 0;\n    while (result !== chunkLen && this.matches < this.maxMatches)\n      result = feed(this, chunk);\n    return result;\n  }\n\n  destroy() {\n    const lbSize = this._lookbehindSize;\n    if (lbSize)\n      this._cb(false, this._lookbehind, 0, lbSize, false);\n    this.reset();\n  }\n}\n\nfunction feed(self, data) {\n  const len = data.length;\n  const needle = self._needle;\n  const needleLen = needle.length;\n\n  // Positive: points to a position in `data`\n  //           pos == 3 points to data[3]\n  // Negative: points to a position in the lookbehind buffer\n  //           pos == -2 points to lookbehind[lookbehindSize - 2]\n  let pos = -self._lookbehindSize;\n  const lastNeedleCharPos = needleLen - 1;\n  const lastNeedleChar = needle[lastNeedleCharPos];\n  const end = len - needleLen;\n  const occ = self._occ;\n  const lookbehind = self._lookbehind;\n\n  if (pos < 0) {\n    // Lookbehind buffer is not empty. Perform Boyer-Moore-Horspool\n    // search with character lookup code that considers both the\n    // lookbehind buffer and the current round's haystack data.\n    //\n    // Loop until\n    //   there is a match.\n    // or until\n    //   we've moved past the position that requires the\n    //   lookbehind buffer. In this case we switch to the\n    //   optimized loop.\n    // or until\n    //   the character to look at lies outside the haystack.\n    while (pos < 0 && pos <= end) {\n      const nextPos = pos + lastNeedleCharPos;\n      const ch = (nextPos < 0\n                  ? lookbehind[self._lookbehindSize + nextPos]\n                  : data[nextPos]);\n\n      if (ch === lastNeedleChar\n          && matchNeedle(self, data, pos, lastNeedleCharPos)) {\n        self._lookbehindSize = 0;\n        ++self.matches;\n        if (pos > -self._lookbehindSize)\n          self._cb(true, lookbehind, 0, self._lookbehindSize + pos, false);\n        else\n          self._cb(true, undefined, 0, 0, true);\n\n        return (self._bufPos = pos + needleLen);\n      }\n\n      pos += occ[ch];\n    }\n\n    // No match.\n\n    // There's too few data for Boyer-Moore-Horspool to run,\n    // so let's use a different algorithm to skip as much as\n    // we can.\n    // Forward pos until\n    //   the trailing part of lookbehind + data\n    //   looks like the beginning of the needle\n    // or until\n    //   pos == 0\n    while (pos < 0 && !matchNeedle(self, data, pos, len - pos))\n      ++pos;\n\n    if (pos < 0) {\n      // Cut off part of the lookbehind buffer that has\n      // been processed and append the entire haystack\n      // into it.\n      const bytesToCutOff = self._lookbehindSize + pos;\n\n      if (bytesToCutOff > 0) {\n        // The cut off data is guaranteed not to contain the needle.\n        self._cb(false, lookbehind, 0, bytesToCutOff, false);\n      }\n\n      self._lookbehindSize -= bytesToCutOff;\n      lookbehind.copy(lookbehind, 0, bytesToCutOff, self._lookbehindSize);\n      lookbehind.set(data, self._lookbehindSize);\n      self._lookbehindSize += len;\n\n      self._bufPos = len;\n      return len;\n    }\n\n    // Discard lookbehind buffer.\n    self._cb(false, lookbehind, 0, self._lookbehindSize, false);\n    self._lookbehindSize = 0;\n  }\n\n  pos += self._bufPos;\n\n  const firstNeedleChar = needle[0];\n\n  // Lookbehind buffer is now empty. Perform Boyer-Moore-Horspool\n  // search with optimized character lookup code that only considers\n  // the current round's haystack data.\n  while (pos <= end) {\n    const ch = data[pos + lastNeedleCharPos];\n\n    if (ch === lastNeedleChar\n        && data[pos] === firstNeedleChar\n        && memcmp(needle, 0, data, pos, lastNeedleCharPos)) {\n      ++self.matches;\n      if (pos > 0)\n        self._cb(true, data, self._bufPos, pos, true);\n      else\n        self._cb(true, undefined, 0, 0, true);\n\n      return (self._bufPos = pos + needleLen);\n    }\n\n    pos += occ[ch];\n  }\n\n  // There was no match. If there's trailing haystack data that we cannot\n  // match yet using the Boyer-Moore-Horspool algorithm (because the trailing\n  // data is less than the needle size) then match using a modified\n  // algorithm that starts matching from the beginning instead of the end.\n  // Whatever trailing data is left after running this algorithm is added to\n  // the lookbehind buffer.\n  while (pos < len) {\n    if (data[pos] !== firstNeedleChar\n        || !memcmp(data, pos, needle, 0, len - pos)) {\n      ++pos;\n      continue;\n    }\n    data.copy(lookbehind, 0, pos, len);\n    self._lookbehindSize = len - pos;\n    break;\n  }\n\n  // Everything until `pos` is guaranteed not to contain needle data.\n  if (pos > 0)\n    self._cb(false, data, self._bufPos, pos < len ? pos : len, true);\n\n  self._bufPos = len;\n  return len;\n}\n\nfunction matchNeedle(self, data, pos, len) {\n  const lb = self._lookbehind;\n  const lbSize = self._lookbehindSize;\n  const needle = self._needle;\n\n  for (let i = 0; i < len; ++i, ++pos) {\n    const ch = (pos < 0 ? lb[lbSize + pos] : data[pos]);\n    if (ch !== needle[i])\n      return false;\n  }\n  return true;\n}\n\nmodule.exports = SBMH;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/streamsearch/lib/sbmh.js\n");

/***/ })

};
;