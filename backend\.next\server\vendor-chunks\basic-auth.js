/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/basic-auth";
exports.ids = ["vendor-chunks/basic-auth"];
exports.modules = {

/***/ "(rsc)/./node_modules/basic-auth/index.js":
/*!******************************************!*\
  !*** ./node_modules/basic-auth/index.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("/*!\n * basic-auth\n * Copyright(c) 2013 TJ Holowaychuk\n * Copyright(c) 2014 Jonathan Ong\n * Copyright(c) 2015-2016 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\n/**\n * Module dependencies.\n * @private\n */\n\nvar Buffer = (__webpack_require__(/*! safe-buffer */ \"(rsc)/./node_modules/basic-auth/node_modules/safe-buffer/index.js\").Buffer)\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = auth\nmodule.exports.parse = parse\n\n/**\n * RegExp for basic auth credentials\n *\n * credentials = auth-scheme 1*SP token68\n * auth-scheme = \"Basic\" ; case insensitive\n * token68     = 1*( ALPHA / DIGIT / \"-\" / \".\" / \"_\" / \"~\" / \"+\" / \"/\" ) *\"=\"\n * @private\n */\n\nvar CREDENTIALS_REGEXP = /^ *(?:[Bb][Aa][Ss][Ii][Cc]) +([A-Za-z0-9._~+/-]+=*) *$/\n\n/**\n * RegExp for basic auth user/pass\n *\n * user-pass   = userid \":\" password\n * userid      = *<TEXT excluding \":\">\n * password    = *TEXT\n * @private\n */\n\nvar USER_PASS_REGEXP = /^([^:]*):(.*)$/\n\n/**\n * Parse the Authorization header field of a request.\n *\n * @param {object} req\n * @return {object} with .name and .pass\n * @public\n */\n\nfunction auth (req) {\n  if (!req) {\n    throw new TypeError('argument req is required')\n  }\n\n  if (typeof req !== 'object') {\n    throw new TypeError('argument req is required to be an object')\n  }\n\n  // get header\n  var header = getAuthorization(req)\n\n  // parse header\n  return parse(header)\n}\n\n/**\n * Decode base64 string.\n * @private\n */\n\nfunction decodeBase64 (str) {\n  return Buffer.from(str, 'base64').toString()\n}\n\n/**\n * Get the Authorization header from request object.\n * @private\n */\n\nfunction getAuthorization (req) {\n  if (!req.headers || typeof req.headers !== 'object') {\n    throw new TypeError('argument req is required to have headers property')\n  }\n\n  return req.headers.authorization\n}\n\n/**\n * Parse basic auth to object.\n *\n * @param {string} string\n * @return {object}\n * @public\n */\n\nfunction parse (string) {\n  if (typeof string !== 'string') {\n    return undefined\n  }\n\n  // parse header\n  var match = CREDENTIALS_REGEXP.exec(string)\n\n  if (!match) {\n    return undefined\n  }\n\n  // decode user pass\n  var userPass = USER_PASS_REGEXP.exec(decodeBase64(match[1]))\n\n  if (!userPass) {\n    return undefined\n  }\n\n  // return credentials object\n  return new Credentials(userPass[1], userPass[2])\n}\n\n/**\n * Object to represent user credentials.\n * @private\n */\n\nfunction Credentials (name, pass) {\n  this.name = name\n  this.pass = pass\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/basic-auth/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/basic-auth/node_modules/safe-buffer/index.js":
/*!*******************************************************************!*\
  !*** ./node_modules/basic-auth/node_modules/safe-buffer/index.js ***!
  \*******************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("/* eslint-disable node/no-deprecated-api */\nvar buffer = __webpack_require__(/*! buffer */ \"buffer\")\nvar Buffer = buffer.Buffer\n\n// alternative to using Object.keys for old browsers\nfunction copyProps (src, dst) {\n  for (var key in src) {\n    dst[key] = src[key]\n  }\n}\nif (Buffer.from && Buffer.alloc && Buffer.allocUnsafe && Buffer.allocUnsafeSlow) {\n  module.exports = buffer\n} else {\n  // Copy properties from require('buffer')\n  copyProps(buffer, exports)\n  exports.Buffer = SafeBuffer\n}\n\nfunction SafeBuffer (arg, encodingOrOffset, length) {\n  return Buffer(arg, encodingOrOffset, length)\n}\n\n// Copy static methods from Buffer\ncopyProps(Buffer, SafeBuffer)\n\nSafeBuffer.from = function (arg, encodingOrOffset, length) {\n  if (typeof arg === 'number') {\n    throw new TypeError('Argument must not be a number')\n  }\n  return Buffer(arg, encodingOrOffset, length)\n}\n\nSafeBuffer.alloc = function (size, fill, encoding) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number')\n  }\n  var buf = Buffer(size)\n  if (fill !== undefined) {\n    if (typeof encoding === 'string') {\n      buf.fill(fill, encoding)\n    } else {\n      buf.fill(fill)\n    }\n  } else {\n    buf.fill(0)\n  }\n  return buf\n}\n\nSafeBuffer.allocUnsafe = function (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number')\n  }\n  return Buffer(size)\n}\n\nSafeBuffer.allocUnsafeSlow = function (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number')\n  }\n  return buffer.SlowBuffer(size)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/basic-auth/node_modules/safe-buffer/index.js\n");

/***/ })

};
;