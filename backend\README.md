# SRSR Property Management Backend

A comprehensive backend API for the SRSR Property Management System built with Next.js, Express.js, and PostgreSQL.

## 🚀 Features

- **Authentication & Authorization**: JWT-based auth with Role-Based Access Control (RBAC)
- **Property Management**: Multi-property support with system monitoring
- **Office Management**: Attendance tracking and employee management
- **Real-time Updates**: WebSocket support for live data
- **System Monitoring**: Water, Electricity, Security, Internet, OTT, Maintenance
- **Dashboard Analytics**: Comprehensive reporting and statistics
- **API Documentation**: Auto-generated Swagger documentation
- **Rate Limiting**: Configurable rate limiting for security
- **File Upload**: Secure file upload with validation
- **Health Checks**: System health monitoring endpoints

## 🛠️ Tech Stack

- **Framework**: Next.js 14 with App Router
- **API**: Express.js with TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: JWT with bcrypt
- **Real-time**: Socket.io
- **Validation**: Zod
- **Documentation**: Swagger/OpenAPI 3.0
- **Security**: Helmet, CORS, Rate Limiting

## 📋 Prerequisites

- Node.js 18+ 
- PostgreSQL 12+
- npm or yarn

## 🚀 Quick Start

### 1. Clone and Install

```bash
cd backend
npm install
```

### 2. Environment Setup

```bash
cp .env.example .env
```

Edit `.env` with your configuration:

```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/srsr_property_management"

# JWT Secrets
JWT_SECRET="your-super-secret-jwt-key"
JWT_REFRESH_SECRET="your-super-secret-refresh-key"

# Server
PORT=3001
NODE_ENV=development
```

### 3. Database Setup

```bash
# Generate Prisma client
npm run db:generate

# Push schema to database
npm run db:push

# Seed with sample data
npm run db:seed
```

### 4. Start Development Server

```bash
# Start Next.js development server
npm run dev

# Or start Express server directly
npm run server:dev
```

The API will be available at:
- **API**: http://localhost:3001/v1
- **Documentation**: http://localhost:3001/api-docs
- **Health Check**: http://localhost:3001/health

## 📚 API Documentation

### Authentication

```bash
# Login
POST /v1/auth/login
{
  "email": "<EMAIL>",
  "password": "admin123"
}

# Get current user
GET /v1/auth/me
Authorization: Bearer <token>
```

### Properties

```bash
# List properties
GET /v1/properties?page=1&limit=20&type=RESIDENTIAL

# Get property details
GET /v1/properties/{propertyId}

# Update system status
PUT /v1/properties/{propertyId}/systems/WATER
{
  "status": "WARNING",
  "description": "Tank level low",
  "healthScore": 75
}
```

### Dashboard

```bash
# Get dashboard overview
GET /v1/dashboard/overview?timeRange=24h

# Get alerts
GET /v1/dashboard/alerts?severity=CRITICAL&status=OPEN
```

### Office Management

```bash
# List offices
GET /v1/offices

# Submit attendance
POST /v1/offices/{officeId}/attendance
{
  "date": "2024-01-15",
  "records": [
    {
      "employeeId": "emp-123",
      "status": "PRESENT",
      "checkInTime": "09:15",
      "checkOutTime": "18:45"
    }
  ]
}
```

## 🔐 User Roles & Permissions

| Role | Dashboard | Properties | Office | Security | Maintenance | Users | Reports |
|------|-----------|------------|--------|----------|-------------|-------|---------|
| **Super Admin** | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| **Property Manager** | ✅ | ✅ (assigned) | ❌ | ✅ | ✅ | ❌ | ✅ |
| **Office Manager** | ✅ | ❌ | ✅ | ❌ | ❌ | ❌ | ✅ |
| **Security Personnel** | ✅ | ❌ | ❌ | ✅ | ❌ | ❌ | ❌ |
| **Maintenance Staff** | ✅ | ❌ | ❌ | ❌ | ✅ | ❌ | ❌ |
| **Construction Supervisor** | ✅ | ❌ | ✅ (sites) | ❌ | ❌ | ❌ | ✅ |



## 📊 Database Schema

### Core Entities

- **Users**: Authentication and RBAC
- **Properties**: Property management with system monitoring
- **SystemStatus**: Real-time system health tracking
- **Offices**: Office and construction site management
- **Employees**: Employee information and management
- **AttendanceRecords**: Daily attendance tracking
- **Alerts**: System alerts and notifications
- **Activities**: Audit trail and activity logging

## 🧪 Testing

```bash
# Run tests
npm test

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm test -- auth.test.ts
```

## 🚀 Deployment

### Production Build

```bash
# Build for production
npm run build

# Start production server
npm start
```

### Environment Variables

```env
NODE_ENV=production
DATABASE_URL="postgresql://..."
JWT_SECRET="production-secret"
CORS_ORIGIN="https://yourdomain.com"
```

### Docker Deployment

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3001
CMD ["npm", "start"]
```

## 📈 Monitoring

### Health Checks

- **Basic**: `GET /health`
- **Detailed**: `GET /health/detailed`

### Metrics

- Database connection status
- Memory usage
- Active connections
- Response times

## 🔧 Configuration

### Rate Limiting

```env
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100
AUTH_RATE_LIMIT_MAX=5
```

### File Upload

```env
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_PATH="./uploads"
ALLOWED_FILE_TYPES="image/jpeg,image/png,image/webp,application/pdf"
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🆘 Support

For support and questions:
- Email: <EMAIL>
- Documentation: http://localhost:3001/api-docs
- Issues: GitHub Issues
