/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rate-limiter-flexible";
exports.ids = ["vendor-chunks/rate-limiter-flexible"];
exports.modules = {

/***/ "(rsc)/./node_modules/rate-limiter-flexible/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/rate-limiter-flexible/index.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const RateLimiterRedis = __webpack_require__(/*! ./lib/RateLimiterRedis */ \"(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterRedis.js\");\nconst RateLimiterMongo = __webpack_require__(/*! ./lib/RateLimiterMongo */ \"(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterMongo.js\");\nconst RateLimiterMySQL = __webpack_require__(/*! ./lib/RateLimiterMySQL */ \"(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterMySQL.js\");\nconst RateLimiterPostgres = __webpack_require__(/*! ./lib/RateLimiterPostgres */ \"(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterPostgres.js\");\nconst {RateLimiterClusterMaster, RateLimiterClusterMasterPM2, RateLimiterCluster} = __webpack_require__(/*! ./lib/RateLimiterCluster */ \"(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterCluster.js\");\nconst RateLimiterMemory = __webpack_require__(/*! ./lib/RateLimiterMemory */ \"(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterMemory.js\");\nconst RateLimiterMemcache = __webpack_require__(/*! ./lib/RateLimiterMemcache */ \"(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterMemcache.js\");\nconst RLWrapperBlackAndWhite = __webpack_require__(/*! ./lib/RLWrapperBlackAndWhite */ \"(rsc)/./node_modules/rate-limiter-flexible/lib/RLWrapperBlackAndWhite.js\");\nconst RateLimiterUnion = __webpack_require__(/*! ./lib/RateLimiterUnion */ \"(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterUnion.js\");\nconst RateLimiterQueue = __webpack_require__(/*! ./lib/RateLimiterQueue */ \"(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterQueue.js\");\nconst BurstyRateLimiter = __webpack_require__(/*! ./lib/BurstyRateLimiter */ \"(rsc)/./node_modules/rate-limiter-flexible/lib/BurstyRateLimiter.js\");\nconst RateLimiterRes = __webpack_require__(/*! ./lib/RateLimiterRes */ \"(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterRes.js\");\n\nmodule.exports = {\n  RateLimiterRedis,\n  RateLimiterMongo,\n  RateLimiterMySQL,\n  RateLimiterPostgres,\n  RateLimiterMemory,\n  RateLimiterMemcache,\n  RateLimiterClusterMaster,\n  RateLimiterClusterMasterPM2,\n  RateLimiterCluster,\n  RLWrapperBlackAndWhite,\n  RateLimiterUnion,\n  RateLimiterQueue,\n  BurstyRateLimiter,\n  RateLimiterRes,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/rate-limiter-flexible/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/rate-limiter-flexible/lib/BurstyRateLimiter.js":
/*!*********************************************************************!*\
  !*** ./node_modules/rate-limiter-flexible/lib/BurstyRateLimiter.js ***!
  \*********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const RateLimiterRes = __webpack_require__(/*! ./RateLimiterRes */ \"(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterRes.js\");\n\n/**\n * Bursty rate limiter exposes only msBeforeNext time and doesn't expose points from bursty limiter by default\n * @type {BurstyRateLimiter}\n */\nmodule.exports = class BurstyRateLimiter {\n  constructor(rateLimiter, burstLimiter) {\n    this._rateLimiter = rateLimiter;\n    this._burstLimiter = burstLimiter\n  }\n\n  /**\n   * Merge rate limiter response objects. Responses can be null\n   *\n   * @param {RateLimiterRes} [rlRes] Rate limiter response\n   * @param {RateLimiterRes} [blRes] Bursty limiter response\n   */\n  _combineRes(rlRes, blRes) {\n    if (!rlRes) {\n      return null\n    }\n\n    return new RateLimiterRes(\n      rlRes.remainingPoints,\n      Math.min(rlRes.msBeforeNext, blRes ? blRes.msBeforeNext : 0),\n      rlRes.consumedPoints,\n      rlRes.isFirstInDuration\n    )\n  }\n\n  /**\n   * @param key\n   * @param pointsToConsume\n   * @param options\n   * @returns {Promise<any>}\n   */\n  consume(key, pointsToConsume = 1, options = {}) {\n    return this._rateLimiter.consume(key, pointsToConsume, options)\n      .catch((rlRej) => {\n        if (rlRej instanceof RateLimiterRes) {\n          return this._burstLimiter.consume(key, pointsToConsume, options)\n            .then((blRes) => {\n              return Promise.resolve(this._combineRes(rlRej, blRes))\n            })\n            .catch((blRej) => {\n                if (blRej instanceof RateLimiterRes) {\n                  return Promise.reject(this._combineRes(rlRej, blRej))\n                } else {\n                  return Promise.reject(blRej)\n                }\n              }\n            )\n        } else {\n          return Promise.reject(rlRej)\n        }\n      })\n  }\n\n  /**\n   * It doesn't expose available points from burstLimiter\n   *\n   * @param key\n   * @returns {Promise<RateLimiterRes>}\n   */\n  get(key) {\n    return Promise.all([\n      this._rateLimiter.get(key),\n      this._burstLimiter.get(key),\n    ]).then(([rlRes, blRes]) => {\n      return this._combineRes(rlRes, blRes);\n    });\n  }\n\n  get points() {\n    return this._rateLimiter.points;\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/rate-limiter-flexible/lib/BurstyRateLimiter.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/rate-limiter-flexible/lib/RLWrapperBlackAndWhite.js":
/*!**************************************************************************!*\
  !*** ./node_modules/rate-limiter-flexible/lib/RLWrapperBlackAndWhite.js ***!
  \**************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const RateLimiterRes = __webpack_require__(/*! ./RateLimiterRes */ \"(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterRes.js\");\n\nmodule.exports = class RLWrapperBlackAndWhite {\n  constructor(opts = {}) {\n    this.limiter = opts.limiter;\n    this.blackList = opts.blackList;\n    this.whiteList = opts.whiteList;\n    this.isBlackListed = opts.isBlackListed;\n    this.isWhiteListed = opts.isWhiteListed;\n    this.runActionAnyway = opts.runActionAnyway;\n  }\n\n  get limiter() {\n    return this._limiter;\n  }\n\n  set limiter(value) {\n    if (typeof value === 'undefined') {\n      throw new Error('limiter is not set');\n    }\n\n    this._limiter = value;\n  }\n\n  get runActionAnyway() {\n    return this._runActionAnyway;\n  }\n\n  set runActionAnyway(value) {\n    this._runActionAnyway = typeof value === 'undefined' ? false : value;\n  }\n\n  get blackList() {\n    return this._blackList;\n  }\n\n  set blackList(value) {\n    this._blackList = Array.isArray(value) ? value : [];\n  }\n\n  get isBlackListed() {\n    return this._isBlackListed;\n  }\n\n  set isBlackListed(func) {\n    if (typeof func === 'undefined') {\n      func = () => false;\n    }\n    if (typeof func !== 'function') {\n      throw new Error('isBlackListed must be function');\n    }\n    this._isBlackListed = func;\n  }\n\n  get whiteList() {\n    return this._whiteList;\n  }\n\n  set whiteList(value) {\n    this._whiteList = Array.isArray(value) ? value : [];\n  }\n\n  get isWhiteListed() {\n    return this._isWhiteListed;\n  }\n\n  set isWhiteListed(func) {\n    if (typeof func === 'undefined') {\n      func = () => false;\n    }\n    if (typeof func !== 'function') {\n      throw new Error('isWhiteListed must be function');\n    }\n    this._isWhiteListed = func;\n  }\n\n  isBlackListedSomewhere(key) {\n    return this.blackList.indexOf(key) >= 0 || this.isBlackListed(key);\n  }\n\n  isWhiteListedSomewhere(key) {\n    return this.whiteList.indexOf(key) >= 0 || this.isWhiteListed(key);\n  }\n\n  getBlackRes() {\n    return new RateLimiterRes(0, Number.MAX_SAFE_INTEGER, 0, false);\n  }\n\n  getWhiteRes() {\n    return new RateLimiterRes(Number.MAX_SAFE_INTEGER, 0, 0, false);\n  }\n\n  rejectBlack() {\n    return Promise.reject(this.getBlackRes());\n  }\n\n  resolveBlack() {\n    return Promise.resolve(this.getBlackRes());\n  }\n\n  resolveWhite() {\n    return Promise.resolve(this.getWhiteRes());\n  }\n\n  consume(key, pointsToConsume = 1) {\n    let res;\n    if (this.isWhiteListedSomewhere(key)) {\n      res = this.resolveWhite();\n    } else if (this.isBlackListedSomewhere(key)) {\n      res = this.rejectBlack();\n    }\n\n    if (typeof res === 'undefined') {\n      return this.limiter.consume(key, pointsToConsume);\n    }\n\n    if (this.runActionAnyway) {\n      this.limiter.consume(key, pointsToConsume).catch(() => {});\n    }\n    return res;\n  }\n\n  block(key, secDuration) {\n    let res;\n    if (this.isWhiteListedSomewhere(key)) {\n      res = this.resolveWhite();\n    } else if (this.isBlackListedSomewhere(key)) {\n      res = this.resolveBlack();\n    }\n\n    if (typeof res === 'undefined') {\n      return this.limiter.block(key, secDuration);\n    }\n\n    if (this.runActionAnyway) {\n      this.limiter.block(key, secDuration).catch(() => {});\n    }\n    return res;\n  }\n\n  penalty(key, points) {\n    let res;\n    if (this.isWhiteListedSomewhere(key)) {\n      res = this.resolveWhite();\n    } else if (this.isBlackListedSomewhere(key)) {\n      res = this.resolveBlack();\n    }\n\n    if (typeof res === 'undefined') {\n      return this.limiter.penalty(key, points);\n    }\n\n    if (this.runActionAnyway) {\n      this.limiter.penalty(key, points).catch(() => {});\n    }\n    return res;\n  }\n\n  reward(key, points) {\n    let res;\n    if (this.isWhiteListedSomewhere(key)) {\n      res = this.resolveWhite();\n    } else if (this.isBlackListedSomewhere(key)) {\n      res = this.resolveBlack();\n    }\n\n    if (typeof res === 'undefined') {\n      return this.limiter.reward(key, points);\n    }\n\n    if (this.runActionAnyway) {\n      this.limiter.reward(key, points).catch(() => {});\n    }\n    return res;\n  }\n\n  get(key) {\n    let res;\n    if (this.isWhiteListedSomewhere(key)) {\n      res = this.resolveWhite();\n    } else if (this.isBlackListedSomewhere(key)) {\n      res = this.resolveBlack();\n    }\n\n    if (typeof res === 'undefined' || this.runActionAnyway) {\n      return this.limiter.get(key);\n    }\n\n    return res;\n  }\n\n  delete(key) {\n    return this.limiter.delete(key);\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/rate-limiter-flexible/lib/RLWrapperBlackAndWhite.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterAbstract.js":
/*!***********************************************************************!*\
  !*** ./node_modules/rate-limiter-flexible/lib/RateLimiterAbstract.js ***!
  \***********************************************************************/
/***/ ((module) => {

eval("module.exports = class RateLimiterAbstract {\n  /**\n   *\n   * @param opts Object Defaults {\n   *   points: 4, // Number of points\n   *   duration: 1, // Per seconds\n   *   blockDuration: 0, // Block if consumed more than points in current duration for blockDuration seconds\n   *   execEvenly: false, // Execute allowed actions evenly over duration\n   *   execEvenlyMinDelayMs: duration * 1000 / points, // ms, works with execEvenly=true option\n   *   keyPrefix: 'rlflx',\n   * }\n   */\n  constructor(opts = {}) {\n    this.points = opts.points;\n    this.duration = opts.duration;\n    this.blockDuration = opts.blockDuration;\n    this.execEvenly = opts.execEvenly;\n    this.execEvenlyMinDelayMs = opts.execEvenlyMinDelayMs;\n    this.keyPrefix = opts.keyPrefix;\n  }\n\n  get points() {\n    return this._points;\n  }\n\n  set points(value) {\n    this._points = value >= 0 ? value : 4;\n  }\n\n  get duration() {\n    return this._duration;\n  }\n\n  set duration(value) {\n    this._duration = typeof value === 'undefined' ? 1 : value;\n  }\n\n  get msDuration() {\n    return this.duration * 1000;\n  }\n\n  get blockDuration() {\n    return this._blockDuration;\n  }\n\n  set blockDuration(value) {\n    this._blockDuration = typeof value === 'undefined' ? 0 : value;\n  }\n\n  get msBlockDuration() {\n    return this.blockDuration * 1000;\n  }\n\n  get execEvenly() {\n    return this._execEvenly;\n  }\n\n  set execEvenly(value) {\n    this._execEvenly = typeof value === 'undefined' ? false : Boolean(value);\n  }\n\n  get execEvenlyMinDelayMs() {\n    return this._execEvenlyMinDelayMs;\n  }\n\n  set execEvenlyMinDelayMs(value) {\n    this._execEvenlyMinDelayMs = typeof value === 'undefined' ? Math.ceil(this.msDuration / this.points) : value;\n  }\n\n  get keyPrefix() {\n    return this._keyPrefix;\n  }\n\n  set keyPrefix(value) {\n    if (typeof value === 'undefined') {\n      value = 'rlflx';\n    }\n    if (typeof value !== 'string') {\n      throw new Error('keyPrefix must be string');\n    }\n    this._keyPrefix = value;\n  }\n\n  _getKeySecDuration(options = {}) {\n    return options && options.customDuration >= 0\n      ? options.customDuration\n      : this.duration;\n  }\n\n  getKey(key) {\n    return this.keyPrefix.length > 0 ? `${this.keyPrefix}:${key}` : key;\n  }\n\n  parseKey(rlKey) {\n    return rlKey.substring(this.keyPrefix.length);\n  }\n\n  consume() {\n    throw new Error(\"You have to implement the method 'consume'!\");\n  }\n\n  penalty() {\n    throw new Error(\"You have to implement the method 'penalty'!\");\n  }\n\n  reward() {\n    throw new Error(\"You have to implement the method 'reward'!\");\n  }\n\n  get() {\n    throw new Error(\"You have to implement the method 'get'!\");\n  }\n\n  set() {\n    throw new Error(\"You have to implement the method 'set'!\");\n  }\n\n  block() {\n    throw new Error(\"You have to implement the method 'block'!\");\n  }\n\n  delete() {\n    throw new Error(\"You have to implement the method 'delete'!\");\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterAbstract.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterCluster.js":
/*!**********************************************************************!*\
  !*** ./node_modules/rate-limiter-flexible/lib/RateLimiterCluster.js ***!
  \**********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * Implements rate limiting in cluster using built-in IPC\n *\n * Two classes are described here: master and worker\n * Master have to be create in the master process without any options.\n * Any number of rate limiters can be created in workers, but each rate limiter must be with unique keyPrefix\n *\n * Workflow:\n * 1. master rate limiter created in master process\n * 2. worker rate limiter sends 'init' message with necessary options during creating\n * 3. master receives options and adds new rate limiter by keyPrefix if it isn't created yet\n * 4. master sends 'init' back to worker's rate limiter\n * 5. worker can process requests immediately,\n *    but they will be postponed by 'workerWaitInit' until master sends 'init' to worker\n * 6. every request to worker rate limiter creates a promise\n * 7. if master doesn't response for 'timeout', promise is rejected\n * 8. master sends 'resolve' or 'reject' command to worker\n * 9. worker resolves or rejects promise depending on message from master\n *\n */\n\nconst cluster = __webpack_require__(/*! cluster */ \"cluster\");\nconst crypto = __webpack_require__(/*! crypto */ \"crypto\");\nconst RateLimiterAbstract = __webpack_require__(/*! ./RateLimiterAbstract */ \"(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterAbstract.js\");\nconst RateLimiterMemory = __webpack_require__(/*! ./RateLimiterMemory */ \"(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterMemory.js\");\nconst RateLimiterRes = __webpack_require__(/*! ./RateLimiterRes */ \"(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterRes.js\");\n\nconst channel = 'rate_limiter_flexible';\nlet masterInstance = null;\n\nconst masterSendToWorker = function (worker, msg, type, res) {\n  let data;\n  if (res === null || res === true || res === false) {\n    data = res;\n  } else {\n    data = {\n      remainingPoints: res.remainingPoints,\n      msBeforeNext: res.msBeforeNext,\n      consumedPoints: res.consumedPoints,\n      isFirstInDuration: res.isFirstInDuration,\n    };\n  }\n  worker.send({\n    channel,\n    keyPrefix: msg.keyPrefix, // which rate limiter exactly\n    promiseId: msg.promiseId,\n    type,\n    data,\n  });\n};\n\nconst workerWaitInit = function (payload) {\n  setTimeout(() => {\n    if (this._initiated) {\n      process.send(payload);\n      // Promise will be removed by timeout if too long\n    } else if (typeof this._promises[payload.promiseId] !== 'undefined') {\n      workerWaitInit.call(this, payload);\n    }\n  }, 30);\n};\n\nconst workerSendToMaster = function (func, promiseId, key, arg, opts) {\n  const payload = {\n    channel,\n    keyPrefix: this.keyPrefix,\n    func,\n    promiseId,\n    data: {\n      key,\n      arg,\n      opts,\n    },\n  };\n\n  if (!this._initiated) {\n    // Wait init before sending messages to master\n    workerWaitInit.call(this, payload);\n  } else {\n    process.send(payload);\n  }\n};\n\nconst masterProcessMsg = function (worker, msg) {\n  if (!msg || msg.channel !== channel || typeof this._rateLimiters[msg.keyPrefix] === 'undefined') {\n    return false;\n  }\n\n  let promise;\n\n  switch (msg.func) {\n    case 'consume':\n      promise = this._rateLimiters[msg.keyPrefix].consume(msg.data.key, msg.data.arg, msg.data.opts);\n      break;\n    case 'penalty':\n      promise = this._rateLimiters[msg.keyPrefix].penalty(msg.data.key, msg.data.arg, msg.data.opts);\n      break;\n    case 'reward':\n      promise = this._rateLimiters[msg.keyPrefix].reward(msg.data.key, msg.data.arg, msg.data.opts);\n      break;\n    case 'block':\n      promise = this._rateLimiters[msg.keyPrefix].block(msg.data.key, msg.data.arg, msg.data.opts);\n      break;\n    case 'get':\n      promise = this._rateLimiters[msg.keyPrefix].get(msg.data.key, msg.data.opts);\n      break;\n    case 'delete':\n      promise = this._rateLimiters[msg.keyPrefix].delete(msg.data.key, msg.data.opts);\n      break;\n    default:\n      return false;\n  }\n\n  if (promise) {\n    promise\n      .then((res) => {\n        masterSendToWorker(worker, msg, 'resolve', res);\n      })\n      .catch((rejRes) => {\n        masterSendToWorker(worker, msg, 'reject', rejRes);\n      });\n  }\n};\n\nconst workerProcessMsg = function (msg) {\n  if (!msg || msg.channel !== channel || msg.keyPrefix !== this.keyPrefix) {\n    return false;\n  }\n\n  if (this._promises[msg.promiseId]) {\n    clearTimeout(this._promises[msg.promiseId].timeoutId);\n    let res;\n    if (msg.data === null || msg.data === true || msg.data === false) {\n      res = msg.data;\n    } else {\n      res = new RateLimiterRes(\n        msg.data.remainingPoints,\n        msg.data.msBeforeNext,\n        msg.data.consumedPoints,\n        msg.data.isFirstInDuration // eslint-disable-line comma-dangle\n      );\n    }\n\n    switch (msg.type) {\n      case 'resolve':\n        this._promises[msg.promiseId].resolve(res);\n        break;\n      case 'reject':\n        this._promises[msg.promiseId].reject(res);\n        break;\n      default:\n        throw new Error(`RateLimiterCluster: no such message type '${msg.type}'`);\n    }\n\n    delete this._promises[msg.promiseId];\n  }\n};\n/**\n * Prepare options to send to master\n * Master will create rate limiter depending on options\n *\n * @returns {{points: *, duration: *, blockDuration: *, execEvenly: *, execEvenlyMinDelayMs: *, keyPrefix: *}}\n */\nconst getOpts = function () {\n  return {\n    points: this.points,\n    duration: this.duration,\n    blockDuration: this.blockDuration,\n    execEvenly: this.execEvenly,\n    execEvenlyMinDelayMs: this.execEvenlyMinDelayMs,\n    keyPrefix: this.keyPrefix,\n  };\n};\n\nconst savePromise = function (resolve, reject) {\n  const hrtime = process.hrtime();\n  let promiseId = hrtime[0].toString() + hrtime[1].toString();\n\n  if (typeof this._promises[promiseId] !== 'undefined') {\n    promiseId += crypto.randomBytes(12).toString('base64');\n  }\n\n  this._promises[promiseId] = {\n    resolve,\n    reject,\n    timeoutId: setTimeout(() => {\n      delete this._promises[promiseId];\n      reject(new Error('RateLimiterCluster timeout: no answer from master in time'));\n    }, this.timeoutMs),\n  };\n\n  return promiseId;\n};\n\nclass RateLimiterClusterMaster {\n  constructor() {\n    if (masterInstance) {\n      return masterInstance;\n    }\n\n    this._rateLimiters = {};\n\n    cluster.setMaxListeners(0);\n\n    cluster.on('message', (worker, msg) => {\n      if (msg && msg.channel === channel && msg.type === 'init') {\n        // If init request, check or create rate limiter by key prefix and send 'init' back to worker\n        if (typeof this._rateLimiters[msg.opts.keyPrefix] === 'undefined') {\n          this._rateLimiters[msg.opts.keyPrefix] = new RateLimiterMemory(msg.opts);\n        }\n\n        worker.send({\n          channel,\n          type: 'init',\n          keyPrefix: msg.opts.keyPrefix,\n        });\n      } else {\n        masterProcessMsg.call(this, worker, msg);\n      }\n    });\n\n    masterInstance = this;\n  }\n}\n\nclass RateLimiterClusterMasterPM2 {\n  constructor(pm2) {\n    if (masterInstance) {\n      return masterInstance;\n    }\n\n    this._rateLimiters = {};\n\n    pm2.launchBus((err, pm2Bus) => {\n      pm2Bus.on('process:msg', (packet) => {\n        const msg = packet.raw;\n        if (msg && msg.channel === channel && msg.type === 'init') {\n          // If init request, check or create rate limiter by key prefix and send 'init' back to worker\n          if (typeof this._rateLimiters[msg.opts.keyPrefix] === 'undefined') {\n            this._rateLimiters[msg.opts.keyPrefix] = new RateLimiterMemory(msg.opts);\n          }\n\n          pm2.sendDataToProcessId(packet.process.pm_id, {\n            data: {},\n            topic: channel,\n            channel,\n            type: 'init',\n            keyPrefix: msg.opts.keyPrefix,\n          }, (sendErr, res) => {\n            if (sendErr) {\n              console.log(sendErr, res);\n            }\n          });\n        } else {\n          const worker = {\n            send: (msgData) => {\n              const pm2Message = msgData;\n              pm2Message.topic = channel;\n              if (typeof pm2Message.data === 'undefined') {\n                pm2Message.data = {};\n              }\n              pm2.sendDataToProcessId(packet.process.pm_id, pm2Message, (sendErr, res) => {\n                if (sendErr) {\n                  console.log(sendErr, res);\n                }\n              });\n            },\n          };\n          masterProcessMsg.call(this, worker, msg);\n        }\n      });\n    });\n\n    masterInstance = this;\n  }\n}\n\nclass RateLimiterClusterWorker extends RateLimiterAbstract {\n  get timeoutMs() {\n    return this._timeoutMs;\n  }\n\n  set timeoutMs(value) {\n    this._timeoutMs = typeof value === 'undefined' ? 5000 : Math.abs(parseInt(value));\n  }\n\n  constructor(opts = {}) {\n    super(opts);\n\n    process.setMaxListeners(0);\n\n    this.timeoutMs = opts.timeoutMs;\n\n    this._initiated = false;\n\n    process.on('message', (msg) => {\n      if (msg && msg.channel === channel && msg.type === 'init' && msg.keyPrefix === this.keyPrefix) {\n        this._initiated = true;\n      } else {\n        workerProcessMsg.call(this, msg);\n      }\n    });\n\n    // Create limiter on master with specific options\n    process.send({\n      channel,\n      type: 'init',\n      opts: getOpts.call(this),\n    });\n\n    this._promises = {};\n  }\n\n  consume(key, pointsToConsume = 1, options = {}) {\n    return new Promise((resolve, reject) => {\n      const promiseId = savePromise.call(this, resolve, reject);\n\n      workerSendToMaster.call(this, 'consume', promiseId, key, pointsToConsume, options);\n    });\n  }\n\n  penalty(key, points = 1, options = {}) {\n    return new Promise((resolve, reject) => {\n      const promiseId = savePromise.call(this, resolve, reject);\n\n      workerSendToMaster.call(this, 'penalty', promiseId, key, points, options);\n    });\n  }\n\n  reward(key, points = 1, options = {}) {\n    return new Promise((resolve, reject) => {\n      const promiseId = savePromise.call(this, resolve, reject);\n\n      workerSendToMaster.call(this, 'reward', promiseId, key, points, options);\n    });\n  }\n\n  block(key, secDuration, options = {}) {\n    return new Promise((resolve, reject) => {\n      const promiseId = savePromise.call(this, resolve, reject);\n\n      workerSendToMaster.call(this, 'block', promiseId, key, secDuration, options);\n    });\n  }\n\n  get(key, options = {}) {\n    return new Promise((resolve, reject) => {\n      const promiseId = savePromise.call(this, resolve, reject);\n\n      workerSendToMaster.call(this, 'get', promiseId, key, options);\n    });\n  }\n\n  delete(key, options = {}) {\n    return new Promise((resolve, reject) => {\n      const promiseId = savePromise.call(this, resolve, reject);\n\n      workerSendToMaster.call(this, 'delete', promiseId, key, options);\n    });\n  }\n}\n\nmodule.exports = {\n  RateLimiterClusterMaster,\n  RateLimiterClusterMasterPM2,\n  RateLimiterCluster: RateLimiterClusterWorker,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterCluster.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterMemcache.js":
/*!***********************************************************************!*\
  !*** ./node_modules/rate-limiter-flexible/lib/RateLimiterMemcache.js ***!
  \***********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const RateLimiterStoreAbstract = __webpack_require__(/*! ./RateLimiterStoreAbstract */ \"(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterStoreAbstract.js\");\nconst RateLimiterRes = __webpack_require__(/*! ./RateLimiterRes */ \"(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterRes.js\");\n\nclass RateLimiterMemcache extends RateLimiterStoreAbstract {\n  /**\n   *\n   * @param {Object} opts\n   * Defaults {\n   *   ... see other in RateLimiterStoreAbstract\n   *\n   *   storeClient: memcacheClient\n   * }\n   */\n  constructor(opts) {\n    super(opts);\n\n    this.client = opts.storeClient;\n  }\n\n  _getRateLimiterRes(rlKey, changedPoints, result) {\n    const res = new RateLimiterRes();\n    res.consumedPoints = parseInt(result.consumedPoints);\n    res.isFirstInDuration = result.consumedPoints === changedPoints;\n    res.remainingPoints = Math.max(this.points - res.consumedPoints, 0);\n    res.msBeforeNext = result.msBeforeNext;\n\n    return res;\n  }\n\n  _upsert(rlKey, points, msDuration, forceExpire = false, options = {}) {\n    return new Promise((resolve, reject) => {\n      const nowMs = Date.now();\n      const secDuration = Math.floor(msDuration / 1000);\n\n      if (forceExpire) {\n        this.client.set(rlKey, points, secDuration, (err) => {\n          if (!err) {\n            this.client.set(\n              `${rlKey}_expire`,\n              secDuration > 0 ? nowMs + (secDuration * 1000) : -1,\n              secDuration,\n              () => {\n                const res = {\n                  consumedPoints: points,\n                  msBeforeNext: secDuration > 0 ? secDuration * 1000 : -1,\n                };\n                resolve(res);\n              }\n            );\n          } else {\n            reject(err);\n          }\n        });\n      } else {\n        this.client.incr(rlKey, points, (err, consumedPoints) => {\n          if (err || consumedPoints === false) {\n            this.client.add(rlKey, points, secDuration, (errAddKey, createdNew) => {\n              if (errAddKey || !createdNew) {\n                // Try to upsert again in case of race condition\n                if (typeof options.attemptNumber === 'undefined' || options.attemptNumber < 3) {\n                  const nextOptions = Object.assign({}, options);\n                  nextOptions.attemptNumber = nextOptions.attemptNumber ? (nextOptions.attemptNumber + 1) : 1;\n\n                  this._upsert(rlKey, points, msDuration, forceExpire, nextOptions)\n                    .then(resUpsert => resolve(resUpsert))\n                    .catch(errUpsert => reject(errUpsert));\n                } else {\n                  reject(new Error('Can not add key'));\n                }\n              } else {\n                this.client.add(\n                  `${rlKey}_expire`,\n                  secDuration > 0 ? nowMs + (secDuration * 1000) : -1,\n                  secDuration,\n                  () => {\n                    const res = {\n                      consumedPoints: points,\n                      msBeforeNext: secDuration > 0 ? secDuration * 1000 : -1,\n                    };\n                    resolve(res);\n                  }\n                );\n              }\n            });\n          } else {\n            this.client.get(`${rlKey}_expire`, (errGetExpire, resGetExpireMs) => {\n              if (errGetExpire) {\n                reject(errGetExpire);\n              } else {\n                const expireMs = resGetExpireMs === false ? 0 : resGetExpireMs;\n                const res = {\n                  consumedPoints,\n                  msBeforeNext: expireMs >= 0 ? Math.max(expireMs - nowMs, 0) : -1,\n                };\n                resolve(res);\n              }\n            });\n          }\n        });\n      }\n    });\n  }\n\n  _get(rlKey) {\n    return new Promise((resolve, reject) => {\n      const nowMs = Date.now();\n\n      this.client.get(rlKey, (err, consumedPoints) => {\n        if (!consumedPoints) {\n          resolve(null);\n        } else {\n          this.client.get(`${rlKey}_expire`, (errGetExpire, resGetExpireMs) => {\n            if (errGetExpire) {\n              reject(errGetExpire);\n            } else {\n              const expireMs = resGetExpireMs === false ? 0 : resGetExpireMs;\n              const res = {\n                consumedPoints,\n                msBeforeNext: expireMs >= 0 ? Math.max(expireMs - nowMs, 0) : -1,\n              };\n              resolve(res);\n            }\n          });\n        }\n      });\n    });\n  }\n\n  _delete(rlKey) {\n    return new Promise((resolve, reject) => {\n      this.client.del(rlKey, (err, res) => {\n        if (err) {\n          reject(err);\n        } else if (res === false) {\n          resolve(res);\n        } else {\n          this.client.del(`${rlKey}_expire`, (errDelExpire) => {\n            if (errDelExpire) {\n              reject(errDelExpire);\n            } else {\n              resolve(res);\n            }\n          });\n        }\n      });\n    });\n  }\n}\n\nmodule.exports = RateLimiterMemcache;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterMemcache.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterMemory.js":
/*!*********************************************************************!*\
  !*** ./node_modules/rate-limiter-flexible/lib/RateLimiterMemory.js ***!
  \*********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const RateLimiterAbstract = __webpack_require__(/*! ./RateLimiterAbstract */ \"(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterAbstract.js\");\nconst MemoryStorage = __webpack_require__(/*! ./component/MemoryStorage/MemoryStorage */ \"(rsc)/./node_modules/rate-limiter-flexible/lib/component/MemoryStorage/MemoryStorage.js\");\nconst RateLimiterRes = __webpack_require__(/*! ./RateLimiterRes */ \"(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterRes.js\");\n\nclass RateLimiterMemory extends RateLimiterAbstract {\n  constructor(opts = {}) {\n    super(opts);\n\n    this._memoryStorage = new MemoryStorage();\n  }\n  /**\n   *\n   * @param key\n   * @param pointsToConsume\n   * @param {Object} options\n   * @returns {Promise<RateLimiterRes>}\n   */\n  consume(key, pointsToConsume = 1, options = {}) {\n    return new Promise((resolve, reject) => {\n      const rlKey = this.getKey(key);\n      const secDuration = this._getKeySecDuration(options);\n      let res = this._memoryStorage.incrby(rlKey, pointsToConsume, secDuration);\n      res.remainingPoints = Math.max(this.points - res.consumedPoints, 0);\n\n      if (res.consumedPoints > this.points) {\n        // Block only first time when consumed more than points\n        if (this.blockDuration > 0 && res.consumedPoints <= (this.points + pointsToConsume)) {\n          // Block key\n          res = this._memoryStorage.set(rlKey, res.consumedPoints, this.blockDuration);\n        }\n        reject(res);\n      } else if (this.execEvenly && res.msBeforeNext > 0 && !res.isFirstInDuration) {\n        // Execute evenly\n        let delay = Math.ceil(res.msBeforeNext / (res.remainingPoints + 2));\n        if (delay < this.execEvenlyMinDelayMs) {\n          delay = res.consumedPoints * this.execEvenlyMinDelayMs;\n        }\n\n        setTimeout(resolve, delay, res);\n      } else {\n        resolve(res);\n      }\n    });\n  }\n\n  penalty(key, points = 1, options = {}) {\n    const rlKey = this.getKey(key);\n    return new Promise((resolve) => {\n      const secDuration = this._getKeySecDuration(options);\n      const res = this._memoryStorage.incrby(rlKey, points, secDuration);\n      res.remainingPoints = Math.max(this.points - res.consumedPoints, 0);\n      resolve(res);\n    });\n  }\n\n  reward(key, points = 1, options = {}) {\n    const rlKey = this.getKey(key);\n    return new Promise((resolve) => {\n      const secDuration = this._getKeySecDuration(options);\n      const res = this._memoryStorage.incrby(rlKey, -points, secDuration);\n      res.remainingPoints = Math.max(this.points - res.consumedPoints, 0);\n      resolve(res);\n    });\n  }\n\n  /**\n   * Block any key for secDuration seconds\n   *\n   * @param key\n   * @param secDuration\n   */\n  block(key, secDuration) {\n    const msDuration = secDuration * 1000;\n    const initPoints = this.points + 1;\n\n    this._memoryStorage.set(this.getKey(key), initPoints, secDuration);\n    return Promise.resolve(\n      new RateLimiterRes(0, msDuration === 0 ? -1 : msDuration, initPoints)\n    );\n  }\n\n  set(key, points, secDuration) {\n    const msDuration = (secDuration >= 0 ? secDuration : this.duration) * 1000;\n\n    this._memoryStorage.set(this.getKey(key), points, secDuration);\n    return Promise.resolve(\n      new RateLimiterRes(0, msDuration === 0 ? -1 : msDuration, points)\n    );\n  }\n\n  get(key) {\n    const res = this._memoryStorage.get(this.getKey(key));\n    if (res !== null) {\n      res.remainingPoints = Math.max(this.points - res.consumedPoints, 0);\n    }\n\n    return Promise.resolve(res);\n  }\n\n  delete(key) {\n    return Promise.resolve(this._memoryStorage.delete(this.getKey(key)));\n  }\n}\n\nmodule.exports = RateLimiterMemory;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterMemory.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterMongo.js":
/*!********************************************************************!*\
  !*** ./node_modules/rate-limiter-flexible/lib/RateLimiterMongo.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const RateLimiterStoreAbstract = __webpack_require__(/*! ./RateLimiterStoreAbstract */ \"(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterStoreAbstract.js\");\nconst RateLimiterRes = __webpack_require__(/*! ./RateLimiterRes */ \"(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterRes.js\");\n\n/**\n * Get MongoDB driver version as upsert options differ\n * @params {Object} Client instance\n * @returns {Object} Version Object containing major, feature & minor versions.\n */\nfunction getDriverVersion(client) {\n  try {\n    const _client = client.client ? client.client : client;\n\n    const { version } = _client.topology.s.options.metadata.driver;\n    const _v = version.split('.').map(v => parseInt(v));\n\n    return {\n      major: _v[0],\n      feature: _v[1],\n      patch: _v[2],\n    };\n  } catch (err) {\n    return { major: 0, feature: 0, patch: 0 };\n  }\n}\n\nclass RateLimiterMongo extends RateLimiterStoreAbstract {\n  /**\n   *\n   * @param {Object} opts\n   * Defaults {\n   *   indexKeyPrefix: {attr1: 1, attr2: 1}\n   *   ... see other in RateLimiterStoreAbstract\n   *\n   *   mongo: MongoClient\n   * }\n   */\n  constructor(opts) {\n    super(opts);\n\n    this.dbName = opts.dbName;\n    this.tableName = opts.tableName;\n    this.indexKeyPrefix = opts.indexKeyPrefix;\n\n    if (opts.mongo) {\n      this.client = opts.mongo;\n    } else {\n      this.client = opts.storeClient;\n    }\n    if (typeof this.client.then === 'function') {\n      // If Promise\n      this.client\n        .then((conn) => {\n          this.client = conn;\n          this._initCollection();\n          this._driverVersion = getDriverVersion(this.client);\n        });\n    } else {\n      this._initCollection();\n      this._driverVersion = getDriverVersion(this.client);\n    }\n  }\n\n  get dbName() {\n    return this._dbName;\n  }\n\n  set dbName(value) {\n    this._dbName = typeof value === 'undefined' ? RateLimiterMongo.getDbName() : value;\n  }\n\n  static getDbName() {\n    return 'node-rate-limiter-flexible';\n  }\n\n  get tableName() {\n    return this._tableName;\n  }\n\n  set tableName(value) {\n    this._tableName = typeof value === 'undefined' ? this.keyPrefix : value;\n  }\n\n  get client() {\n    return this._client;\n  }\n\n  set client(value) {\n    if (typeof value === 'undefined') {\n      throw new Error('mongo is not set');\n    }\n    this._client = value;\n  }\n\n  get indexKeyPrefix() {\n    return this._indexKeyPrefix;\n  }\n\n  set indexKeyPrefix(obj) {\n    this._indexKeyPrefix = obj || {};\n  }\n\n  _initCollection() {\n    const db = typeof this.client.db === 'function'\n      ? this.client.db(this.dbName)\n      : this.client;\n\n    const collection = db.collection(this.tableName);\n    collection.createIndex({ expire: -1 }, { expireAfterSeconds: 0 });\n    collection.createIndex(Object.assign({}, this.indexKeyPrefix, { key: 1 }), { unique: true });\n\n    this._collection = collection;\n  }\n\n  _getRateLimiterRes(rlKey, changedPoints, result) {\n    const res = new RateLimiterRes();\n\n    let doc;\n    if (typeof result.value === 'undefined') {\n      doc = result;\n    } else {\n      doc = result.value;\n    }\n\n    res.isFirstInDuration = doc.points === changedPoints;\n    res.consumedPoints = doc.points;\n\n    res.remainingPoints = Math.max(this.points - res.consumedPoints, 0);\n    res.msBeforeNext = doc.expire !== null\n      ? Math.max(new Date(doc.expire).getTime() - Date.now(), 0)\n      : -1;\n\n    return res;\n  }\n\n  _upsert(key, points, msDuration, forceExpire = false, options = {}) {\n    if (!this._collection) {\n      return Promise.reject(Error('Mongo connection is not established'));\n    }\n\n    const docAttrs = options.attrs || {};\n\n    let where;\n    let upsertData;\n    if (forceExpire) {\n      where = { key };\n      where = Object.assign(where, docAttrs);\n      upsertData = {\n        $set: {\n          key,\n          points,\n          expire: msDuration > 0 ? new Date(Date.now() + msDuration) : null,\n        },\n      };\n      upsertData.$set = Object.assign(upsertData.$set, docAttrs);\n    } else {\n      where = {\n        $or: [\n          { expire: { $gt: new Date() } },\n          { expire: { $eq: null } },\n        ],\n        key,\n      };\n      where = Object.assign(where, docAttrs);\n      upsertData = {\n        $setOnInsert: {\n          key,\n          expire: msDuration > 0 ? new Date(Date.now() + msDuration) : null,\n        },\n        $inc: { points },\n      };\n      upsertData.$setOnInsert = Object.assign(upsertData.$setOnInsert, docAttrs);\n    }\n\n    // Options for collection updates differ between driver versions\n    const upsertOptions = {\n      upsert: true,\n    };\n    if ((this._driverVersion.major >= 4) ||\n        (this._driverVersion.major === 3 &&\n          (this._driverVersion.feature >=7) || \n          (this._driverVersion.feature >= 6 && \n              this._driverVersion.patch >= 7 ))) \n    {\n      upsertOptions.returnDocument = 'after';\n    } else {\n      upsertOptions.returnOriginal = false;\n    }\n\n    /*\n     * 1. Find actual limit and increment points\n     * 2. If limit expired, but Mongo doesn't clean doc by TTL yet, try to replace limit doc completely\n     * 3. If 2 or more Mongo threads try to insert the new limit doc, only the first succeed\n     * 4. Try to upsert from step 1. Actual limit is created now, points are incremented without problems\n     */\n    return new Promise((resolve, reject) => {\n      this._collection.findOneAndUpdate(\n        where,\n        upsertData,\n        upsertOptions\n      ).then((res) => {\n        resolve(res);\n      }).catch((errUpsert) => {\n        if (errUpsert && errUpsert.code === 11000) { // E11000 duplicate key error collection\n          const replaceWhere = Object.assign({ // try to replace OLD limit doc\n            $or: [\n              { expire: { $lte: new Date() } },\n              { expire: { $eq: null } },\n            ],\n            key,\n          }, docAttrs);\n\n          const replaceTo = {\n            $set: Object.assign({\n              key,\n              points,\n              expire: msDuration > 0 ? new Date(Date.now() + msDuration) : null,\n            }, docAttrs)\n          };\n\n          this._collection.findOneAndUpdate(\n            replaceWhere,\n            replaceTo,\n            upsertOptions\n          ).then((res) => {\n            resolve(res);\n          }).catch((errReplace) => {\n            if (errReplace && errReplace.code === 11000) { // E11000 duplicate key error collection\n              this._upsert(key, points, msDuration, forceExpire)\n                .then(res => resolve(res))\n                .catch(err => reject(err));\n            } else {\n              reject(errReplace);\n            }\n          });\n        } else {\n          reject(errUpsert);\n        }\n      });\n    });\n  }\n\n  _get(rlKey, options = {}) {\n    if (!this._collection) {\n      return Promise.reject(Error('Mongo connection is not established'));\n    }\n\n    const docAttrs = options.attrs || {};\n\n    const where = Object.assign({\n      key: rlKey,\n      $or: [\n        { expire: { $gt: new Date() } },\n        { expire: { $eq: null } },\n      ],\n    }, docAttrs);\n\n    return this._collection.findOne(where);\n  }\n\n  _delete(rlKey, options = {}) {\n    if (!this._collection) {\n      return Promise.reject(Error('Mongo connection is not established'));\n    }\n\n    const docAttrs = options.attrs || {};\n    const where = Object.assign({ key: rlKey }, docAttrs);\n\n    return this._collection.deleteOne(where)\n      .then(res => res.deletedCount > 0);\n  }\n}\n\nmodule.exports = RateLimiterMongo;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterMongo.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterMySQL.js":
/*!********************************************************************!*\
  !*** ./node_modules/rate-limiter-flexible/lib/RateLimiterMySQL.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const RateLimiterStoreAbstract = __webpack_require__(/*! ./RateLimiterStoreAbstract */ \"(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterStoreAbstract.js\");\nconst RateLimiterRes = __webpack_require__(/*! ./RateLimiterRes */ \"(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterRes.js\");\n\nclass RateLimiterMySQL extends RateLimiterStoreAbstract {\n  /**\n   * @callback callback\n   * @param {Object} err\n   *\n   * @param {Object} opts\n   * @param {callback} cb\n   * Defaults {\n   *   ... see other in RateLimiterStoreAbstract\n   *\n   *   storeClient: anySqlClient,\n   *   storeType: 'knex', // required only for Knex instance\n   *   dbName: 'string',\n   *   tableName: 'string',\n   * }\n   */\n  constructor(opts, cb = null) {\n    super(opts);\n\n    this.client = opts.storeClient;\n    this.clientType = opts.storeType;\n\n    this.dbName = opts.dbName;\n    this.tableName = opts.tableName;\n\n    this.clearExpiredByTimeout = opts.clearExpiredByTimeout;\n\n    this.tableCreated = opts.tableCreated;\n    if (!this.tableCreated) {\n      this._createDbAndTable()\n        .then(() => {\n          this.tableCreated = true;\n          if (this.clearExpiredByTimeout) {\n            this._clearExpiredHourAgo();\n          }\n          if (typeof cb === 'function') {\n            cb();\n          }\n        })\n        .catch((err) => {\n          if (typeof cb === 'function') {\n            cb(err);\n          } else {\n            throw err;\n          }\n        });\n    } else {\n      if (this.clearExpiredByTimeout) {\n        this._clearExpiredHourAgo();\n      }\n      if (typeof cb === 'function') {\n        cb();\n      }\n    }\n  }\n\n  clearExpired(expire) {\n    return new Promise((resolve) => {\n      this._getConnection()\n        .then((conn) => {\n          conn.query(`DELETE FROM ??.?? WHERE expire < ?`, [this.dbName, this.tableName, expire], () => {\n            this._releaseConnection(conn);\n            resolve();\n          });\n        })\n        .catch(() => {\n          resolve();\n        });\n    });\n  }\n\n  _clearExpiredHourAgo() {\n    if (this._clearExpiredTimeoutId) {\n      clearTimeout(this._clearExpiredTimeoutId);\n    }\n    this._clearExpiredTimeoutId = setTimeout(() => {\n      this.clearExpired(Date.now() - 3600000) // Never rejected\n        .then(() => {\n          this._clearExpiredHourAgo();\n        });\n    }, 300000);\n    this._clearExpiredTimeoutId.unref();\n  }\n\n  /**\n   *\n   * @return Promise<any>\n   * @private\n   */\n  _getConnection() {\n    switch (this.clientType) {\n      case 'pool':\n        return new Promise((resolve, reject) => {\n          this.client.getConnection((errConn, conn) => {\n            if (errConn) {\n              return reject(errConn);\n            }\n\n            resolve(conn);\n          });\n        });\n      case 'sequelize':\n        return this.client.connectionManager.getConnection();\n      case 'knex':\n        return this.client.client.acquireConnection();\n      default:\n        return Promise.resolve(this.client);\n    }\n  }\n\n  _releaseConnection(conn) {\n    switch (this.clientType) {\n      case 'pool':\n        return conn.release();\n      case 'sequelize':\n        return this.client.connectionManager.releaseConnection(conn);\n      case 'knex':\n        return this.client.client.releaseConnection(conn);\n      default:\n        return true;\n    }\n  }\n\n  /**\n   *\n   * @returns {Promise<any>}\n   * @private\n   */\n  _createDbAndTable() {\n    return new Promise((resolve, reject) => {\n      this._getConnection()\n        .then((conn) => {\n          conn.query(`CREATE DATABASE IF NOT EXISTS \\`${this.dbName}\\`;`, (errDb) => {\n            if (errDb) {\n              this._releaseConnection(conn);\n              return reject(errDb);\n            }\n            conn.query(this._getCreateTableStmt(), (err) => {\n              if (err) {\n                this._releaseConnection(conn);\n                return reject(err);\n              }\n              this._releaseConnection(conn);\n              resolve();\n            });\n          });\n        })\n        .catch((err) => {\n          reject(err);\n        });\n    });\n  }\n\n  _getCreateTableStmt() {\n    return `CREATE TABLE IF NOT EXISTS \\`${this.dbName}\\`.\\`${this.tableName}\\` (` +\n      '`key` VARCHAR(255) CHARACTER SET utf8 NOT NULL,' +\n      '`points` INT(9) NOT NULL default 0,' +\n      '`expire` BIGINT UNSIGNED,' +\n      'PRIMARY KEY (`key`)' +\n      ') ENGINE = INNODB;';\n  }\n\n  get clientType() {\n    return this._clientType;\n  }\n\n  set clientType(value) {\n    if (typeof value === 'undefined') {\n      if (this.client.constructor.name === 'Connection') {\n        value = 'connection';\n      } else if (this.client.constructor.name === 'Pool') {\n        value = 'pool';\n      } else if (this.client.constructor.name === 'Sequelize') {\n        value = 'sequelize';\n      } else {\n        throw new Error('storeType is not defined');\n      }\n    }\n    this._clientType = value.toLowerCase();\n  }\n\n  get dbName() {\n    return this._dbName;\n  }\n\n  set dbName(value) {\n    this._dbName = typeof value === 'undefined' ? 'rtlmtrflx' : value;\n  }\n\n  get tableName() {\n    return this._tableName;\n  }\n\n  set tableName(value) {\n    this._tableName = typeof value === 'undefined' ? this.keyPrefix : value;\n  }\n\n  get tableCreated() {\n    return this._tableCreated\n  }\n\n  set tableCreated(value) {\n    this._tableCreated = typeof value === 'undefined' ? false : !!value;\n  }\n\n  get clearExpiredByTimeout() {\n    return this._clearExpiredByTimeout;\n  }\n\n  set clearExpiredByTimeout(value) {\n    this._clearExpiredByTimeout = typeof value === 'undefined' ? true : Boolean(value);\n  }\n\n  _getRateLimiterRes(rlKey, changedPoints, result) {\n    const res = new RateLimiterRes();\n    const [row] = result;\n\n    res.isFirstInDuration = changedPoints === row.points;\n    res.consumedPoints = res.isFirstInDuration ? changedPoints : row.points;\n\n    res.remainingPoints = Math.max(this.points - res.consumedPoints, 0);\n    res.msBeforeNext = row.expire\n      ? Math.max(row.expire - Date.now(), 0)\n      : -1;\n\n    return res;\n  }\n\n  _upsertTransaction(conn, key, points, msDuration, forceExpire) {\n    return new Promise((resolve, reject) => {\n      conn.query('BEGIN', (errBegin) => {\n        if (errBegin) {\n          conn.rollback();\n\n          return reject(errBegin);\n        }\n\n        const dateNow = Date.now();\n        const newExpire = msDuration > 0 ? dateNow + msDuration : null;\n\n        let q;\n        let values;\n        if (forceExpire) {\n          q = `INSERT INTO ??.?? VALUES (?, ?, ?)\n          ON DUPLICATE KEY UPDATE \n            points = ?, \n            expire = ?;`;\n          values = [\n            this.dbName, this.tableName, key, points, newExpire,\n            points,\n            newExpire,\n          ];\n        } else {\n          q = `INSERT INTO ??.?? VALUES (?, ?, ?)\n          ON DUPLICATE KEY UPDATE \n            points = IF(expire <= ?, ?, points + (?)), \n            expire = IF(expire <= ?, ?, expire);`;\n          values = [\n            this.dbName, this.tableName, key, points, newExpire,\n            dateNow, points, points,\n            dateNow, newExpire,\n          ];\n        }\n\n        conn.query(q, values, (errUpsert) => {\n          if (errUpsert) {\n            conn.rollback();\n\n            return reject(errUpsert);\n          }\n          conn.query('SELECT points, expire FROM ??.?? WHERE `key` = ?;', [this.dbName, this.tableName, key], (errSelect, res) => {\n            if (errSelect) {\n              conn.rollback();\n\n              return reject(errSelect);\n            }\n\n            conn.query('COMMIT', (err) => {\n              if (err) {\n                conn.rollback();\n\n                return reject(err);\n              }\n\n              resolve(res);\n            });\n          });\n        });\n      });\n    });\n  }\n\n  _upsert(key, points, msDuration, forceExpire = false) {\n    if (!this.tableCreated) {\n      return Promise.reject(Error('Table is not created yet'));\n    }\n\n    return new Promise((resolve, reject) => {\n      this._getConnection()\n        .then((conn) => {\n          this._upsertTransaction(conn, key, points, msDuration, forceExpire)\n            .then((res) => {\n              resolve(res);\n              this._releaseConnection(conn);\n            })\n            .catch((err) => {\n              reject(err);\n              this._releaseConnection(conn);\n            });\n        })\n        .catch((err) => {\n          reject(err);\n        });\n    });\n  }\n\n  _get(rlKey) {\n    if (!this.tableCreated) {\n      return Promise.reject(Error('Table is not created yet'));\n    }\n\n    return new Promise((resolve, reject) => {\n      this._getConnection()\n        .then((conn) => {\n          conn.query(\n            'SELECT points, expire FROM ??.?? WHERE `key` = ? AND (`expire` > ? OR `expire` IS NULL)',\n            [this.dbName, this.tableName, rlKey, Date.now()],\n            (err, res) => {\n              if (err) {\n                reject(err);\n              } else if (res.length === 0) {\n                resolve(null);\n              } else {\n                resolve(res);\n              }\n\n              this._releaseConnection(conn);\n            } // eslint-disable-line\n          );\n        })\n        .catch((err) => {\n          reject(err);\n        });\n    });\n  }\n\n  _delete(rlKey) {\n    if (!this.tableCreated) {\n      return Promise.reject(Error('Table is not created yet'));\n    }\n\n    return new Promise((resolve, reject) => {\n      this._getConnection()\n        .then((conn) => {\n          conn.query(\n            'DELETE FROM ??.?? WHERE `key` = ?',\n            [this.dbName, this.tableName, rlKey],\n            (err, res) => {\n              if (err) {\n                reject(err);\n              } else {\n                resolve(res.affectedRows > 0);\n              }\n\n              this._releaseConnection(conn);\n            } // eslint-disable-line\n          );\n        })\n        .catch((err) => {\n          reject(err);\n        });\n    });\n  }\n}\n\nmodule.exports = RateLimiterMySQL;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterMySQL.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterPostgres.js":
/*!***********************************************************************!*\
  !*** ./node_modules/rate-limiter-flexible/lib/RateLimiterPostgres.js ***!
  \***********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const RateLimiterStoreAbstract = __webpack_require__(/*! ./RateLimiterStoreAbstract */ \"(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterStoreAbstract.js\");\nconst RateLimiterRes = __webpack_require__(/*! ./RateLimiterRes */ \"(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterRes.js\");\n\nclass RateLimiterPostgres extends RateLimiterStoreAbstract {\n  /**\n   * @callback callback\n   * @param {Object} err\n   *\n   * @param {Object} opts\n   * @param {callback} cb\n   * Defaults {\n   *   ... see other in RateLimiterStoreAbstract\n   *\n   *   storeClient: postgresClient,\n   *   storeType: 'knex', // required only for Knex instance\n   *   tableName: 'string',\n   * }\n   */\n  constructor(opts, cb = null) {\n    super(opts);\n\n    this.client = opts.storeClient;\n    this.clientType = opts.storeType;\n\n    this.tableName = opts.tableName;\n\n    this.clearExpiredByTimeout = opts.clearExpiredByTimeout;\n\n    this.tableCreated = opts.tableCreated;\n    if (!this.tableCreated) {\n      this._createTable()\n        .then(() => {\n          this.tableCreated = true;\n          if (this.clearExpiredByTimeout) {\n            this._clearExpiredHourAgo();\n          }\n          if (typeof cb === 'function') {\n            cb();\n          }\n        })\n        .catch((err) => {\n          if (typeof cb === 'function') {\n            cb(err);\n          } else {\n            throw err;\n          }\n        });\n    } else {\n      if (typeof cb === 'function') {\n        cb();\n      }\n    }\n  }\n\n  clearExpired(expire) {\n    return new Promise((resolve) => {\n      const q = {\n        name: 'rlflx-clear-expired',\n        text: `DELETE FROM ${this.tableName} WHERE expire < $1`,\n        values: [expire],\n      };\n      this._query(q)\n        .then(() => {\n          resolve();\n        })\n        .catch(() => {\n          // Deleting expired query is not critical\n          resolve();\n        });\n    });\n  }\n\n  /**\n   * Delete all rows expired 1 hour ago once per 5 minutes\n   *\n   * @private\n   */\n  _clearExpiredHourAgo() {\n    if (this._clearExpiredTimeoutId) {\n      clearTimeout(this._clearExpiredTimeoutId);\n    }\n    this._clearExpiredTimeoutId = setTimeout(() => {\n      this.clearExpired(Date.now() - 3600000) // Never rejected\n        .then(() => {\n          this._clearExpiredHourAgo();\n        });\n    }, 300000);\n    this._clearExpiredTimeoutId.unref();\n  }\n\n  /**\n   *\n   * @return Promise<any>\n   * @private\n   */\n  _getConnection() {\n    switch (this.clientType) {\n      case 'pool':\n        return Promise.resolve(this.client);\n      case 'sequelize':\n        return this.client.connectionManager.getConnection();\n      case 'knex':\n        return this.client.client.acquireConnection();\n      case 'typeorm':\n        return Promise.resolve(this.client.driver.master);\n      default:\n        return Promise.resolve(this.client);\n    }\n  }\n\n  _releaseConnection(conn) {\n    switch (this.clientType) {\n      case 'pool':\n        return true;\n      case 'sequelize':\n        return this.client.connectionManager.releaseConnection(conn);\n      case 'knex':\n        return this.client.client.releaseConnection(conn);\n      case 'typeorm':\n        return true;\n      default:\n        return true;\n    }\n  }\n\n  /**\n   *\n   * @returns {Promise<any>}\n   * @private\n   */\n  _createTable() {\n    return new Promise((resolve, reject) => {\n      this._query({\n        text: this._getCreateTableStmt(),\n      })\n        .then(() => {\n          resolve();\n        })\n        .catch((err) => {\n          if (err.code === '23505') {\n            // Error: duplicate key value violates unique constraint \"pg_type_typname_nsp_index\"\n            // Postgres doesn't handle concurrent table creation\n            // It is supposed, that table is created by another worker\n            resolve();\n          } else {\n            reject(err);\n          }\n        });\n    });\n  }\n\n  _getCreateTableStmt() {\n    return `CREATE TABLE IF NOT EXISTS ${this.tableName} ( \n      key varchar(255) PRIMARY KEY,\n      points integer NOT NULL DEFAULT 0,\n      expire bigint\n    );`;\n  }\n\n  get clientType() {\n    return this._clientType;\n  }\n\n  set clientType(value) {\n    const constructorName = this.client.constructor.name;\n\n    if (typeof value === 'undefined') {\n      if (constructorName === 'Client') {\n        value = 'client';\n      } else if (\n        constructorName === 'Pool' ||\n        constructorName === 'BoundPool'\n      ) {\n        value = 'pool';\n      } else if (constructorName === 'Sequelize') {\n        value = 'sequelize';\n      } else {\n        throw new Error('storeType is not defined');\n      }\n    }\n\n    this._clientType = value.toLowerCase();\n  }\n\n  get tableName() {\n    return this._tableName;\n  }\n\n  set tableName(value) {\n    this._tableName = typeof value === 'undefined' ? this.keyPrefix : value;\n  }\n\n  get tableCreated() {\n    return this._tableCreated\n  }\n\n  set tableCreated(value) {\n    this._tableCreated = typeof value === 'undefined' ? false : !!value;\n  }\n\n  get clearExpiredByTimeout() {\n    return this._clearExpiredByTimeout;\n  }\n\n  set clearExpiredByTimeout(value) {\n    this._clearExpiredByTimeout = typeof value === 'undefined' ? true : Boolean(value);\n  }\n\n  _getRateLimiterRes(rlKey, changedPoints, result) {\n    const res = new RateLimiterRes();\n    const row = result.rows[0];\n\n    res.isFirstInDuration = changedPoints === row.points;\n    res.consumedPoints = res.isFirstInDuration ? changedPoints : row.points;\n\n    res.remainingPoints = Math.max(this.points - res.consumedPoints, 0);\n    res.msBeforeNext = row.expire\n      ? Math.max(row.expire - Date.now(), 0)\n      : -1;\n\n    return res;\n  }\n\n  _query(q) {\n    const prefix = this.tableName.toLowerCase();\n    const queryObj = { name: `${prefix}:${q.name}`, text: q.text, values: q.values };\n    return new Promise((resolve, reject) => {\n      this._getConnection()\n        .then((conn) => {\n          conn.query(queryObj)\n            .then((res) => {\n              resolve(res);\n              this._releaseConnection(conn);\n            })\n            .catch((err) => {\n              reject(err);\n              this._releaseConnection(conn);\n            });\n        })\n        .catch((err) => {\n          reject(err);\n        });\n    });\n  }\n\n  _upsert(key, points, msDuration, forceExpire = false) {\n    if (!this.tableCreated) {\n      return Promise.reject(Error('Table is not created yet'));\n    }\n\n    const newExpire = msDuration > 0 ? Date.now() + msDuration : null;\n    const expireQ = forceExpire\n      ? ' $3 '\n      : ` CASE\n             WHEN ${this.tableName}.expire <= $4 THEN $3\n             ELSE ${this.tableName}.expire\n            END `;\n\n    return this._query({\n      name: forceExpire ? 'rlflx-upsert-force' : 'rlflx-upsert',\n      text: `\n            INSERT INTO ${this.tableName} VALUES ($1, $2, $3)\n              ON CONFLICT(key) DO UPDATE SET\n                points = CASE\n                          WHEN (${this.tableName}.expire <= $4 OR 1=${forceExpire ? 1 : 0}) THEN $2\n                          ELSE ${this.tableName}.points + ($2)\n                         END,\n                expire = ${expireQ}\n            RETURNING points, expire;`,\n      values: [key, points, newExpire, Date.now()],\n    });\n  }\n\n  _get(rlKey) {\n    if (!this.tableCreated) {\n      return Promise.reject(Error('Table is not created yet'));\n    }\n\n    return new Promise((resolve, reject) => {\n      this._query({\n        name: 'rlflx-get',\n        text: `\n            SELECT points, expire FROM ${this.tableName} WHERE key = $1 AND (expire > $2 OR expire IS NULL);`,\n        values: [rlKey, Date.now()],\n      })\n        .then((res) => {\n          if (res.rowCount === 0) {\n            res = null;\n          }\n          resolve(res);\n        })\n        .catch((err) => {\n          reject(err);\n        });\n    });\n  }\n\n  _delete(rlKey) {\n    if (!this.tableCreated) {\n      return Promise.reject(Error('Table is not created yet'));\n    }\n\n    return this._query({\n      name: 'rlflx-delete',\n      text: `DELETE FROM ${this.tableName} WHERE key = $1`,\n      values: [rlKey],\n    })\n      .then(res => res.rowCount > 0);\n  }\n}\n\nmodule.exports = RateLimiterPostgres;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterPostgres.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterQueue.js":
/*!********************************************************************!*\
  !*** ./node_modules/rate-limiter-flexible/lib/RateLimiterQueue.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const RateLimiterQueueError = __webpack_require__(/*! ./component/RateLimiterQueueError */ \"(rsc)/./node_modules/rate-limiter-flexible/lib/component/RateLimiterQueueError.js\")\nconst MAX_QUEUE_SIZE = 4294967295;\nconst KEY_DEFAULT = 'limiter';\n\nmodule.exports = class RateLimiterQueue {\n  constructor(limiterFlexible, opts = {\n    maxQueueSize: MAX_QUEUE_SIZE,\n  }) {\n    this._queueLimiters = {\n      KEY_DEFAULT: new RateLimiterQueueInternal(limiterFlexible, opts)\n    };\n    this._limiterFlexible = limiterFlexible;\n    this._maxQueueSize = opts.maxQueueSize\n  }\n\n  getTokensRemaining(key = KEY_DEFAULT) {\n    if (this._queueLimiters[key]) {\n      return this._queueLimiters[key].getTokensRemaining()\n    } else {\n      return Promise.resolve(this._limiterFlexible.points)\n    }\n  }\n\n  removeTokens(tokens, key = KEY_DEFAULT) {\n    if (!this._queueLimiters[key]) {\n      this._queueLimiters[key] = new RateLimiterQueueInternal(\n        this._limiterFlexible, {\n          key,\n          maxQueueSize: this._maxQueueSize,\n        })\n    }\n\n    return this._queueLimiters[key].removeTokens(tokens)\n  }\n};\n\nclass RateLimiterQueueInternal {\n\n  constructor(limiterFlexible, opts = {\n    maxQueueSize: MAX_QUEUE_SIZE,\n    key: KEY_DEFAULT,\n  }) {\n    this._key = opts.key;\n    this._waitTimeout = null;\n    this._queue = [];\n    this._limiterFlexible = limiterFlexible;\n\n    this._maxQueueSize = opts.maxQueueSize\n  }\n\n  getTokensRemaining() {\n    return this._limiterFlexible.get(this._key)\n      .then((rlRes) => {\n        return rlRes !== null ? rlRes.remainingPoints : this._limiterFlexible.points;\n      })\n  }\n\n  removeTokens(tokens) {\n    const _this = this;\n\n    return new Promise((resolve, reject) => {\n      if (tokens > _this._limiterFlexible.points) {\n        reject(new RateLimiterQueueError(`Requested tokens ${tokens} exceeds maximum ${_this._limiterFlexible.points} tokens per interval`));\n        return\n      }\n\n      if (_this._queue.length > 0) {\n        _this._queueRequest.call(_this, resolve, reject, tokens);\n      } else {\n        _this._limiterFlexible.consume(_this._key, tokens)\n          .then((res) => {\n            resolve(res.remainingPoints);\n          })\n          .catch((rej) => {\n            if (rej instanceof Error) {\n              reject(rej);\n            } else {\n              _this._queueRequest.call(_this, resolve, reject, tokens);\n              if (_this._waitTimeout === null) {\n                _this._waitTimeout = setTimeout(_this._processFIFO.bind(_this), rej.msBeforeNext);\n              }\n            }\n          });\n      }\n    })\n  }\n\n  _queueRequest(resolve, reject, tokens) {\n    const _this = this;\n    if (_this._queue.length < _this._maxQueueSize) {\n      _this._queue.push({resolve, reject, tokens});\n    } else {\n      reject(new RateLimiterQueueError(`Number of requests reached it's maximum ${_this._maxQueueSize}`))\n    }\n  }\n\n  _processFIFO() {\n    const _this = this;\n\n    if (_this._waitTimeout !== null) {\n      clearTimeout(_this._waitTimeout);\n      _this._waitTimeout = null;\n    }\n\n    if (_this._queue.length === 0) {\n      return;\n    }\n\n    const item = _this._queue.shift();\n    _this._limiterFlexible.consume(_this._key, item.tokens)\n      .then((res) => {\n        item.resolve(res.remainingPoints);\n        _this._processFIFO.call(_this);\n      })\n      .catch((rej) => {\n        if (rej instanceof Error) {\n          item.reject(rej);\n          _this._processFIFO.call(_this);\n        } else {\n          _this._queue.unshift(item);\n          if (_this._waitTimeout === null) {\n            _this._waitTimeout = setTimeout(_this._processFIFO.bind(_this), rej.msBeforeNext);\n          }\n        }\n      });\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterQueue.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterRedis.js":
/*!********************************************************************!*\
  !*** ./node_modules/rate-limiter-flexible/lib/RateLimiterRedis.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const RateLimiterStoreAbstract = __webpack_require__(/*! ./RateLimiterStoreAbstract */ \"(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterStoreAbstract.js\");\nconst RateLimiterRes = __webpack_require__(/*! ./RateLimiterRes */ \"(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterRes.js\");\n\nconst incrTtlLuaScript = `redis.call('set', KEYS[1], 0, 'EX', ARGV[2], 'NX') \\\nlocal consumed = redis.call('incrby', KEYS[1], ARGV[1]) \\\nlocal ttl = redis.call('pttl', KEYS[1]) \\\nif ttl == -1 then \\\n  redis.call('expire', KEYS[1], ARGV[2]) \\\n  ttl = 1000 * ARGV[2] \\\nend \\\nreturn {consumed, ttl} \\\n`;\n\nclass RateLimiterRedis extends RateLimiterStoreAbstract {\n  /**\n   *\n   * @param {Object} opts\n   * Defaults {\n   *   ... see other in RateLimiterStoreAbstract\n   *\n   *   redis: RedisClient\n   *   rejectIfRedisNotReady: boolean = false - reject / invoke insuranceLimiter immediately when redis connection is not \"ready\"\n   * }\n   */\n  constructor(opts) {\n    super(opts);\n    if (opts.redis) {\n      this.client = opts.redis;\n    } else {\n      this.client = opts.storeClient;\n    }\n\n    this._rejectIfRedisNotReady = !!opts.rejectIfRedisNotReady;\n\n    if (typeof this.client.defineCommand === 'function') {\n      this.client.defineCommand(\"rlflxIncr\", {\n        numberOfKeys: 1,\n        lua: incrTtlLuaScript,\n      });\n    }\n  }\n\n  /**\n   * Prevent actual redis call if redis connection is not ready\n   * Because of different connection state checks for ioredis and node-redis, only this clients would be actually checked.\n   * For any other clients all the requests would be passed directly to redis client\n   * @return {boolean}\n   * @private\n   */\n  _isRedisReady() {\n    if (!this._rejectIfRedisNotReady) {\n      return true;\n    }\n    // ioredis client\n    if (this.client.status && this.client.status !== 'ready') {\n      return false;\n    }\n    // node-redis client\n    if (typeof this.client.isReady === 'function' && !this.client.isReady()) {\n      return false;\n    }\n    return true;\n  }\n\n  _getRateLimiterRes(rlKey, changedPoints, result) {\n    let [consumed, resTtlMs] = result;\n    // Support ioredis results format\n    if (Array.isArray(consumed)) {\n      [, consumed] = consumed;\n      [, resTtlMs] = resTtlMs;\n    }\n\n    const res = new RateLimiterRes();\n    res.consumedPoints = parseInt(consumed);\n    res.isFirstInDuration = res.consumedPoints === changedPoints;\n    res.remainingPoints = Math.max(this.points - res.consumedPoints, 0);\n    res.msBeforeNext = resTtlMs;\n\n    return res;\n  }\n\n  _upsert(rlKey, points, msDuration, forceExpire = false) {\n    return new Promise((resolve, reject) => {\n      if (!this._isRedisReady()) {\n        return reject(new Error('Redis connection is not ready'));\n      }\n\n      const secDuration = Math.floor(msDuration / 1000);\n      const multi = this.client.multi();\n      if (forceExpire) {\n        if (secDuration > 0) {\n          multi.set(rlKey, points, 'EX', secDuration);\n        } else {\n          multi.set(rlKey, points);\n        }\n\n        multi.pttl(rlKey)\n          .exec((err, res) => {\n            if (err) {\n              return reject(err);\n            }\n\n            return resolve(res);\n          });\n      } else {\n        if (secDuration > 0) {\n          const incrCallback = function(err, result) {\n            if (err) {\n              return reject(err);\n            }\n\n            return resolve(result);\n          };\n\n          if (typeof this.client.rlflxIncr === 'function') {\n            this.client.rlflxIncr(rlKey, points, secDuration, incrCallback);\n          } else {\n            this.client.eval(incrTtlLuaScript, 1, rlKey, points, secDuration, incrCallback);\n          }\n        } else {\n          multi.incrby(rlKey, points)\n            .pttl(rlKey)\n            .exec((err, res) => {\n              if (err) {\n                return reject(err);\n              }\n\n              return resolve(res);\n            });\n        }\n      }\n    });\n  }\n\n  _get(rlKey) {\n    return new Promise((resolve, reject) => {\n      if (!this._isRedisReady()) {\n        return reject(new Error('Redis connection is not ready'));\n      }\n\n      this.client\n        .multi()\n        .get(rlKey)\n        .pttl(rlKey)\n        .exec((err, res) => {\n          if (err) {\n            reject(err);\n          } else {\n            const [points] = res;\n            if (points === null) {\n              return resolve(null)\n            }\n\n            resolve(res);\n          }\n        });\n    });\n  }\n\n  _delete(rlKey) {\n    return new Promise((resolve, reject) => {\n      this.client.del(rlKey, (err, res) => {\n        if (err) {\n          reject(err);\n        } else {\n          resolve(res > 0);\n        }\n      });\n    });\n  }\n}\n\nmodule.exports = RateLimiterRedis;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterRedis.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterRes.js":
/*!******************************************************************!*\
  !*** ./node_modules/rate-limiter-flexible/lib/RateLimiterRes.js ***!
  \******************************************************************/
/***/ ((module) => {

eval("module.exports = class RateLimiterRes {\n  constructor(remainingPoints, msBeforeNext, consumedPoints, isFirstInDuration) {\n    this.remainingPoints = typeof remainingPoints === 'undefined' ? 0 : remainingPoints; // Remaining points in current duration\n    this.msBeforeNext = typeof msBeforeNext === 'undefined' ? 0 : msBeforeNext; // Milliseconds before next action\n    this.consumedPoints = typeof consumedPoints === 'undefined' ? 0 : consumedPoints; // Consumed points in current duration\n    this.isFirstInDuration = typeof isFirstInDuration === 'undefined' ? false : isFirstInDuration;\n  }\n\n  get msBeforeNext() {\n    return this._msBeforeNext;\n  }\n\n  set msBeforeNext(ms) {\n    this._msBeforeNext = ms;\n    return this;\n  }\n\n  get remainingPoints() {\n    return this._remainingPoints;\n  }\n\n  set remainingPoints(p) {\n    this._remainingPoints = p;\n    return this;\n  }\n\n  get consumedPoints() {\n    return this._consumedPoints;\n  }\n\n  set consumedPoints(p) {\n    this._consumedPoints = p;\n    return this;\n  }\n\n  get isFirstInDuration() {\n    return this._isFirstInDuration;\n  }\n\n  set isFirstInDuration(value) {\n    this._isFirstInDuration = Boolean(value);\n  }\n\n  _getDecoratedProperties() {\n    return {\n      remainingPoints: this.remainingPoints,\n      msBeforeNext: this.msBeforeNext,\n      consumedPoints: this.consumedPoints,\n      isFirstInDuration: this.isFirstInDuration,\n    };\n  }\n\n  [Symbol.for(\"nodejs.util.inspect.custom\")]() {\n    return this._getDecoratedProperties();\n  }\n\n  toString() {\n    return JSON.stringify(this._getDecoratedProperties());\n  }\n\n  toJSON() {\n    return this._getDecoratedProperties();\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmF0ZS1saW1pdGVyLWZsZXhpYmxlL2xpYi9SYXRlTGltaXRlclJlcy5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0EseUZBQXlGO0FBQ3pGLGdGQUFnRjtBQUNoRixzRkFBc0Y7QUFDdEY7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3Nyc3ItcHJvcGVydHktbWFuYWdlbWVudC1iYWNrZW5kLy4vbm9kZV9tb2R1bGVzL3JhdGUtbGltaXRlci1mbGV4aWJsZS9saWIvUmF0ZUxpbWl0ZXJSZXMuanM/OWEwNCJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9IGNsYXNzIFJhdGVMaW1pdGVyUmVzIHtcbiAgY29uc3RydWN0b3IocmVtYWluaW5nUG9pbnRzLCBtc0JlZm9yZU5leHQsIGNvbnN1bWVkUG9pbnRzLCBpc0ZpcnN0SW5EdXJhdGlvbikge1xuICAgIHRoaXMucmVtYWluaW5nUG9pbnRzID0gdHlwZW9mIHJlbWFpbmluZ1BvaW50cyA9PT0gJ3VuZGVmaW5lZCcgPyAwIDogcmVtYWluaW5nUG9pbnRzOyAvLyBSZW1haW5pbmcgcG9pbnRzIGluIGN1cnJlbnQgZHVyYXRpb25cbiAgICB0aGlzLm1zQmVmb3JlTmV4dCA9IHR5cGVvZiBtc0JlZm9yZU5leHQgPT09ICd1bmRlZmluZWQnID8gMCA6IG1zQmVmb3JlTmV4dDsgLy8gTWlsbGlzZWNvbmRzIGJlZm9yZSBuZXh0IGFjdGlvblxuICAgIHRoaXMuY29uc3VtZWRQb2ludHMgPSB0eXBlb2YgY29uc3VtZWRQb2ludHMgPT09ICd1bmRlZmluZWQnID8gMCA6IGNvbnN1bWVkUG9pbnRzOyAvLyBDb25zdW1lZCBwb2ludHMgaW4gY3VycmVudCBkdXJhdGlvblxuICAgIHRoaXMuaXNGaXJzdEluRHVyYXRpb24gPSB0eXBlb2YgaXNGaXJzdEluRHVyYXRpb24gPT09ICd1bmRlZmluZWQnID8gZmFsc2UgOiBpc0ZpcnN0SW5EdXJhdGlvbjtcbiAgfVxuXG4gIGdldCBtc0JlZm9yZU5leHQoKSB7XG4gICAgcmV0dXJuIHRoaXMuX21zQmVmb3JlTmV4dDtcbiAgfVxuXG4gIHNldCBtc0JlZm9yZU5leHQobXMpIHtcbiAgICB0aGlzLl9tc0JlZm9yZU5leHQgPSBtcztcbiAgICByZXR1cm4gdGhpcztcbiAgfVxuXG4gIGdldCByZW1haW5pbmdQb2ludHMoKSB7XG4gICAgcmV0dXJuIHRoaXMuX3JlbWFpbmluZ1BvaW50cztcbiAgfVxuXG4gIHNldCByZW1haW5pbmdQb2ludHMocCkge1xuICAgIHRoaXMuX3JlbWFpbmluZ1BvaW50cyA9IHA7XG4gICAgcmV0dXJuIHRoaXM7XG4gIH1cblxuICBnZXQgY29uc3VtZWRQb2ludHMoKSB7XG4gICAgcmV0dXJuIHRoaXMuX2NvbnN1bWVkUG9pbnRzO1xuICB9XG5cbiAgc2V0IGNvbnN1bWVkUG9pbnRzKHApIHtcbiAgICB0aGlzLl9jb25zdW1lZFBvaW50cyA9IHA7XG4gICAgcmV0dXJuIHRoaXM7XG4gIH1cblxuICBnZXQgaXNGaXJzdEluRHVyYXRpb24oKSB7XG4gICAgcmV0dXJuIHRoaXMuX2lzRmlyc3RJbkR1cmF0aW9uO1xuICB9XG5cbiAgc2V0IGlzRmlyc3RJbkR1cmF0aW9uKHZhbHVlKSB7XG4gICAgdGhpcy5faXNGaXJzdEluRHVyYXRpb24gPSBCb29sZWFuKHZhbHVlKTtcbiAgfVxuXG4gIF9nZXREZWNvcmF0ZWRQcm9wZXJ0aWVzKCkge1xuICAgIHJldHVybiB7XG4gICAgICByZW1haW5pbmdQb2ludHM6IHRoaXMucmVtYWluaW5nUG9pbnRzLFxuICAgICAgbXNCZWZvcmVOZXh0OiB0aGlzLm1zQmVmb3JlTmV4dCxcbiAgICAgIGNvbnN1bWVkUG9pbnRzOiB0aGlzLmNvbnN1bWVkUG9pbnRzLFxuICAgICAgaXNGaXJzdEluRHVyYXRpb246IHRoaXMuaXNGaXJzdEluRHVyYXRpb24sXG4gICAgfTtcbiAgfVxuXG4gIFtTeW1ib2wuZm9yKFwibm9kZWpzLnV0aWwuaW5zcGVjdC5jdXN0b21cIildKCkge1xuICAgIHJldHVybiB0aGlzLl9nZXREZWNvcmF0ZWRQcm9wZXJ0aWVzKCk7XG4gIH1cblxuICB0b1N0cmluZygpIHtcbiAgICByZXR1cm4gSlNPTi5zdHJpbmdpZnkodGhpcy5fZ2V0RGVjb3JhdGVkUHJvcGVydGllcygpKTtcbiAgfVxuXG4gIHRvSlNPTigpIHtcbiAgICByZXR1cm4gdGhpcy5fZ2V0RGVjb3JhdGVkUHJvcGVydGllcygpO1xuICB9XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterRes.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterStoreAbstract.js":
/*!****************************************************************************!*\
  !*** ./node_modules/rate-limiter-flexible/lib/RateLimiterStoreAbstract.js ***!
  \****************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const RateLimiterAbstract = __webpack_require__(/*! ./RateLimiterAbstract */ \"(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterAbstract.js\");\nconst BlockedKeys = __webpack_require__(/*! ./component/BlockedKeys */ \"(rsc)/./node_modules/rate-limiter-flexible/lib/component/BlockedKeys/index.js\");\nconst RateLimiterRes = __webpack_require__(/*! ./RateLimiterRes */ \"(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterRes.js\");\n\nmodule.exports = class RateLimiterStoreAbstract extends RateLimiterAbstract {\n  /**\n   *\n   * @param opts Object Defaults {\n   *   ... see other in RateLimiterAbstract\n   *\n   *   inMemoryBlockOnConsumed: 40, // Number of points when key is blocked\n   *   inMemoryBlockDuration: 10, // Block duration in seconds\n   *   insuranceLimiter: RateLimiterAbstract\n   * }\n   */\n  constructor(opts = {}) {\n    super(opts);\n\n    this.inMemoryBlockOnConsumed = opts.inMemoryBlockOnConsumed || opts.inmemoryBlockOnConsumed;\n    this.inMemoryBlockDuration = opts.inMemoryBlockDuration || opts.inmemoryBlockDuration;\n    this.insuranceLimiter = opts.insuranceLimiter;\n    this._inMemoryBlockedKeys = new BlockedKeys();\n  }\n\n  get client() {\n    return this._client;\n  }\n\n  set client(value) {\n    if (typeof value === 'undefined') {\n      throw new Error('storeClient is not set');\n    }\n    this._client = value;\n  }\n\n  /**\n   * Have to be launched after consume\n   * It blocks key and execute evenly depending on result from store\n   *\n   * It uses _getRateLimiterRes function to prepare RateLimiterRes from store result\n   *\n   * @param resolve\n   * @param reject\n   * @param rlKey\n   * @param changedPoints\n   * @param storeResult\n   * @param {Object} options\n   * @private\n   */\n  _afterConsume(resolve, reject, rlKey, changedPoints, storeResult, options = {}) {\n    const res = this._getRateLimiterRes(rlKey, changedPoints, storeResult);\n\n    if (this.inMemoryBlockOnConsumed > 0 && !(this.inMemoryBlockDuration > 0)\n      && res.consumedPoints >= this.inMemoryBlockOnConsumed\n    ) {\n      this._inMemoryBlockedKeys.addMs(rlKey, res.msBeforeNext);\n      if (res.consumedPoints > this.points) {\n        return reject(res);\n      } else {\n        return resolve(res)\n      }\n    } else if (res.consumedPoints > this.points) {\n      let blockPromise = Promise.resolve();\n      // Block only first time when consumed more than points\n      if (this.blockDuration > 0 && res.consumedPoints <= (this.points + changedPoints)) {\n        res.msBeforeNext = this.msBlockDuration;\n        blockPromise = this._block(rlKey, res.consumedPoints, this.msBlockDuration, options);\n      }\n\n      if (this.inMemoryBlockOnConsumed > 0 && res.consumedPoints >= this.inMemoryBlockOnConsumed) {\n        // Block key for this.inMemoryBlockDuration seconds\n        this._inMemoryBlockedKeys.add(rlKey, this.inMemoryBlockDuration);\n        res.msBeforeNext = this.msInMemoryBlockDuration;\n      }\n\n      blockPromise\n        .then(() => {\n          reject(res);\n        })\n        .catch((err) => {\n          reject(err);\n        });\n    } else if (this.execEvenly && res.msBeforeNext > 0 && !res.isFirstInDuration) {\n      let delay = Math.ceil(res.msBeforeNext / (res.remainingPoints + 2));\n      if (delay < this.execEvenlyMinDelayMs) {\n        delay = res.consumedPoints * this.execEvenlyMinDelayMs;\n      }\n\n      setTimeout(resolve, delay, res);\n    } else {\n      resolve(res);\n    }\n  }\n\n  _handleError(err, funcName, resolve, reject, key, data = false, options = {}) {\n    if (!(this.insuranceLimiter instanceof RateLimiterAbstract)) {\n      reject(err);\n    } else {\n      this.insuranceLimiter[funcName](key, data, options)\n        .then((res) => {\n          resolve(res);\n        })\n        .catch((res) => {\n          reject(res);\n        });\n    }\n  }\n\n  /**\n   * @deprecated Use camelCase version\n   * @returns {BlockedKeys}\n   * @private\n   */\n  get _inmemoryBlockedKeys() {\n    return this._inMemoryBlockedKeys\n  }\n\n  /**\n   * @deprecated Use camelCase version\n   * @param rlKey\n   * @returns {number}\n   */\n  getInmemoryBlockMsBeforeExpire(rlKey) {\n    return this.getInMemoryBlockMsBeforeExpire(rlKey)\n  }\n\n  /**\n   * @deprecated Use camelCase version\n   * @returns {number|number}\n   */\n  get inmemoryBlockOnConsumed() {\n    return this.inMemoryBlockOnConsumed;\n  }\n\n  /**\n   * @deprecated Use camelCase version\n   * @param value\n   */\n  set inmemoryBlockOnConsumed(value) {\n    this.inMemoryBlockOnConsumed = value;\n  }\n\n  /**\n   * @deprecated Use camelCase version\n   * @returns {number|number}\n   */\n  get inmemoryBlockDuration() {\n    return this.inMemoryBlockDuration;\n  }\n\n  /**\n   * @deprecated Use camelCase version\n   * @param value\n   */\n  set inmemoryBlockDuration(value) {\n    this.inMemoryBlockDuration = value\n  }\n\n  /**\n   * @deprecated Use camelCase version\n   * @returns {number}\n   */\n  get msInmemoryBlockDuration() {\n    return this.inMemoryBlockDuration * 1000;\n  }\n\n  getInMemoryBlockMsBeforeExpire(rlKey) {\n    if (this.inMemoryBlockOnConsumed > 0) {\n      return this._inMemoryBlockedKeys.msBeforeExpire(rlKey);\n    }\n\n    return 0;\n  }\n\n  get inMemoryBlockOnConsumed() {\n    return this._inMemoryBlockOnConsumed;\n  }\n\n  set inMemoryBlockOnConsumed(value) {\n    this._inMemoryBlockOnConsumed = value ? parseInt(value) : 0;\n    if (this.inMemoryBlockOnConsumed > 0 && this.points > this.inMemoryBlockOnConsumed) {\n      throw new Error('inMemoryBlockOnConsumed option must be greater or equal \"points\" option');\n    }\n  }\n\n  get inMemoryBlockDuration() {\n    return this._inMemoryBlockDuration;\n  }\n\n  set inMemoryBlockDuration(value) {\n    this._inMemoryBlockDuration = value ? parseInt(value) : 0;\n    if (this.inMemoryBlockDuration > 0 && this.inMemoryBlockOnConsumed === 0) {\n      throw new Error('inMemoryBlockOnConsumed option must be set up');\n    }\n  }\n\n  get msInMemoryBlockDuration() {\n    return this._inMemoryBlockDuration * 1000;\n  }\n\n  get insuranceLimiter() {\n    return this._insuranceLimiter;\n  }\n\n  set insuranceLimiter(value) {\n    if (typeof value !== 'undefined' && !(value instanceof RateLimiterAbstract)) {\n      throw new Error('insuranceLimiter must be instance of RateLimiterAbstract');\n    }\n    this._insuranceLimiter = value;\n    if (this._insuranceLimiter) {\n      this._insuranceLimiter.blockDuration = this.blockDuration;\n      this._insuranceLimiter.execEvenly = this.execEvenly;\n    }\n  }\n\n  /**\n   * Block any key for secDuration seconds\n   *\n   * @param key\n   * @param secDuration\n   * @param {Object} options\n   *\n   * @return Promise<RateLimiterRes>\n   */\n  block(key, secDuration, options = {}) {\n    const msDuration = secDuration * 1000;\n    return this._block(this.getKey(key), this.points + 1, msDuration, options);\n  }\n\n  /**\n   * Set points by key for any duration\n   *\n   * @param key\n   * @param points\n   * @param secDuration\n   * @param {Object} options\n   *\n   * @return Promise<RateLimiterRes>\n   */\n  set(key, points, secDuration, options = {}) {\n    const msDuration = (secDuration >= 0 ? secDuration : this.duration) * 1000;\n    return this._block(this.getKey(key), points, msDuration, options);\n  }\n\n  /**\n   *\n   * @param key\n   * @param pointsToConsume\n   * @param {Object} options\n   * @returns Promise<RateLimiterRes>\n   */\n  consume(key, pointsToConsume = 1, options = {}) {\n    return new Promise((resolve, reject) => {\n      const rlKey = this.getKey(key);\n\n      const inMemoryBlockMsBeforeExpire = this.getInMemoryBlockMsBeforeExpire(rlKey);\n      if (inMemoryBlockMsBeforeExpire > 0) {\n        return reject(new RateLimiterRes(0, inMemoryBlockMsBeforeExpire));\n      }\n\n      this._upsert(rlKey, pointsToConsume, this._getKeySecDuration(options) * 1000, false, options)\n        .then((res) => {\n          this._afterConsume(resolve, reject, rlKey, pointsToConsume, res);\n        })\n        .catch((err) => {\n          this._handleError(err, 'consume', resolve, reject, key, pointsToConsume, options);\n        });\n    });\n  }\n\n  /**\n   *\n   * @param key\n   * @param points\n   * @param {Object} options\n   * @returns Promise<RateLimiterRes>\n   */\n  penalty(key, points = 1, options = {}) {\n    const rlKey = this.getKey(key);\n    return new Promise((resolve, reject) => {\n      this._upsert(rlKey, points, this._getKeySecDuration(options) * 1000, false, options)\n        .then((res) => {\n          resolve(this._getRateLimiterRes(rlKey, points, res));\n        })\n        .catch((err) => {\n          this._handleError(err, 'penalty', resolve, reject, key, points, options);\n        });\n    });\n  }\n\n  /**\n   *\n   * @param key\n   * @param points\n   * @param {Object} options\n   * @returns Promise<RateLimiterRes>\n   */\n  reward(key, points = 1, options = {}) {\n    const rlKey = this.getKey(key);\n    return new Promise((resolve, reject) => {\n      this._upsert(rlKey, -points, this._getKeySecDuration(options) * 1000, false, options)\n        .then((res) => {\n          resolve(this._getRateLimiterRes(rlKey, -points, res));\n        })\n        .catch((err) => {\n          this._handleError(err, 'reward', resolve, reject, key, points, options);\n        });\n    });\n  }\n\n  /**\n   *\n   * @param key\n   * @param {Object} options\n   * @returns Promise<RateLimiterRes>|null\n   */\n  get(key, options = {}) {\n    const rlKey = this.getKey(key);\n    return new Promise((resolve, reject) => {\n      this._get(rlKey, options)\n        .then((res) => {\n          if (res === null || typeof res === 'undefined') {\n            resolve(null);\n          } else {\n            resolve(this._getRateLimiterRes(rlKey, 0, res));\n          }\n        })\n        .catch((err) => {\n          this._handleError(err, 'get', resolve, reject, key, options);\n        });\n    });\n  }\n\n  /**\n   *\n   * @param key\n   * @param {Object} options\n   * @returns Promise<boolean>\n   */\n  delete(key, options = {}) {\n    const rlKey = this.getKey(key);\n    return new Promise((resolve, reject) => {\n      this._delete(rlKey, options)\n        .then((res) => {\n          this._inMemoryBlockedKeys.delete(rlKey);\n          resolve(res);\n        })\n        .catch((err) => {\n          this._handleError(err, 'delete', resolve, reject, key, options);\n        });\n    });\n  }\n\n  /**\n   * Cleanup keys no-matter expired or not.\n   */\n  deleteInMemoryBlockedAll() {\n    this._inMemoryBlockedKeys.delete();\n  }\n\n  /**\n   * Get RateLimiterRes object filled depending on storeResult, which specific for exact store\n   *\n   * @param rlKey\n   * @param changedPoints\n   * @param storeResult\n   * @private\n   */\n  _getRateLimiterRes(rlKey, changedPoints, storeResult) { // eslint-disable-line no-unused-vars\n    throw new Error(\"You have to implement the method '_getRateLimiterRes'!\");\n  }\n\n  /**\n   * Block key for this.msBlockDuration milliseconds\n   * Usually, it just prolongs lifetime of key\n   *\n   * @param rlKey\n   * @param initPoints\n   * @param msDuration\n   * @param {Object} options\n   *\n   * @return Promise<any>\n   */\n  _block(rlKey, initPoints, msDuration, options = {}) {\n    return new Promise((resolve, reject) => {\n      this._upsert(rlKey, initPoints, msDuration, true, options)\n        .then(() => {\n          resolve(new RateLimiterRes(0, msDuration > 0 ? msDuration : -1, initPoints));\n        })\n        .catch((err) => {\n          this._handleError(err, 'block', resolve, reject, this.parseKey(rlKey), msDuration / 1000, options);\n        });\n    });\n  }\n\n  /**\n   * Have to be implemented in every limiter\n   * Resolve with raw result from Store OR null if rlKey is not set\n   * or Reject with error\n   *\n   * @param rlKey\n   * @param {Object} options\n   * @private\n   *\n   * @return Promise<any>\n   */\n  _get(rlKey, options = {}) { // eslint-disable-line no-unused-vars\n    throw new Error(\"You have to implement the method '_get'!\");\n  }\n\n  /**\n   * Have to be implemented\n   * Resolve with true OR false if rlKey doesn't exist\n   * or Reject with error\n   *\n   * @param rlKey\n   * @param {Object} options\n   * @private\n   *\n   * @return Promise<any>\n   */\n  _delete(rlKey, options = {}) { // eslint-disable-line no-unused-vars\n    throw new Error(\"You have to implement the method '_delete'!\");\n  }\n\n  /**\n   * Have to be implemented\n   * Resolve with object used for {@link _getRateLimiterRes} to generate {@link RateLimiterRes}\n   *\n   * @param {string} rlKey\n   * @param {number} points\n   * @param {number} msDuration\n   * @param {boolean} forceExpire\n   * @param {Object} options\n   * @abstract\n   *\n   * @return Promise<Object>\n   */\n  _upsert(rlKey, points, msDuration, forceExpire = false, options = {}) {\n    throw new Error(\"You have to implement the method '_upsert'!\");\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterStoreAbstract.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterUnion.js":
/*!********************************************************************!*\
  !*** ./node_modules/rate-limiter-flexible/lib/RateLimiterUnion.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const RateLimiterAbstract = __webpack_require__(/*! ./RateLimiterAbstract */ \"(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterAbstract.js\");\n\nmodule.exports = class RateLimiterUnion {\n  constructor(...limiters) {\n    if (limiters.length < 1) {\n      throw new Error('RateLimiterUnion: at least one limiter have to be passed');\n    }\n    limiters.forEach((limiter) => {\n      if (!(limiter instanceof RateLimiterAbstract)) {\n        throw new Error('RateLimiterUnion: all limiters have to be instance of RateLimiterAbstract');\n      }\n    });\n\n    this._limiters = limiters;\n  }\n\n  consume(key, points = 1) {\n    return new Promise((resolve, reject) => {\n      const promises = [];\n      this._limiters.forEach((limiter) => {\n        promises.push(limiter.consume(key, points).catch(rej => ({ rejected: true, rej })));\n      });\n\n      Promise.all(promises)\n        .then((res) => {\n          const resObj = {};\n          let rejected = false;\n\n          res.forEach((item) => {\n            if (item.rejected === true) {\n              rejected = true;\n            }\n          });\n\n          for (let i = 0; i < res.length; i++) {\n            if (rejected && res[i].rejected === true) {\n              resObj[this._limiters[i].keyPrefix] = res[i].rej;\n            } else if (!rejected) {\n              resObj[this._limiters[i].keyPrefix] = res[i];\n            }\n          }\n\n          if (rejected) {\n            reject(resObj);\n          } else {\n            resolve(resObj);\n          }\n        });\n    });\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterUnion.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/rate-limiter-flexible/lib/component/BlockedKeys/BlockedKeys.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/rate-limiter-flexible/lib/component/BlockedKeys/BlockedKeys.js ***!
  \*************************************************************************************/
/***/ ((module) => {

eval("module.exports = class BlockedKeys {\n  constructor() {\n    this._keys = {}; // {'key': 1526279430331}\n    this._addedKeysAmount = 0;\n  }\n\n  collectExpired() {\n    const now = Date.now();\n\n    Object.keys(this._keys).forEach((key) => {\n      if (this._keys[key] <= now) {\n        delete this._keys[key];\n      }\n    });\n\n    this._addedKeysAmount = Object.keys(this._keys).length;\n  }\n\n  /**\n   * Add new blocked key\n   *\n   * @param key String\n   * @param sec Number\n   */\n  add(key, sec) {\n    this.addMs(key, sec * 1000);\n  }\n\n  /**\n   * Add new blocked key for ms\n   *\n   * @param key String\n   * @param ms Number\n   */\n  addMs(key, ms) {\n    this._keys[key] = Date.now() + ms;\n    this._addedKeysAmount++;\n    if (this._addedKeysAmount > 999) {\n      this.collectExpired();\n    }\n  }\n\n  /**\n   * 0 means not blocked\n   *\n   * @param key\n   * @returns {number}\n   */\n  msBeforeExpire(key) {\n    const expire = this._keys[key];\n\n    if (expire && expire >= Date.now()) {\n      this.collectExpired();\n      const now = Date.now();\n      return expire >= now ? expire - now : 0;\n    }\n\n    return 0;\n  }\n\n  /**\n   * If key is not given, delete all data in memory\n   * \n   * @param {string|undefined} key\n   */\n  delete(key) {\n    if (key) {\n      delete this._keys[key];\n    } else {\n      Object.keys(this._keys).forEach((key) => {\n        delete this._keys[key];\n      });\n    }\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmF0ZS1saW1pdGVyLWZsZXhpYmxlL2xpYi9jb21wb25lbnQvQmxvY2tlZEtleXMvQmxvY2tlZEtleXMuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBLHFCQUFxQixJQUFJO0FBQ3pCO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7O0FBRUw7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlO0FBQ2Y7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLGtCQUFrQjtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3Nyc3ItcHJvcGVydHktbWFuYWdlbWVudC1iYWNrZW5kLy4vbm9kZV9tb2R1bGVzL3JhdGUtbGltaXRlci1mbGV4aWJsZS9saWIvY29tcG9uZW50L0Jsb2NrZWRLZXlzL0Jsb2NrZWRLZXlzLmpzP2Q5MzgiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSBjbGFzcyBCbG9ja2VkS2V5cyB7XG4gIGNvbnN0cnVjdG9yKCkge1xuICAgIHRoaXMuX2tleXMgPSB7fTsgLy8geydrZXknOiAxNTI2Mjc5NDMwMzMxfVxuICAgIHRoaXMuX2FkZGVkS2V5c0Ftb3VudCA9IDA7XG4gIH1cblxuICBjb2xsZWN0RXhwaXJlZCgpIHtcbiAgICBjb25zdCBub3cgPSBEYXRlLm5vdygpO1xuXG4gICAgT2JqZWN0LmtleXModGhpcy5fa2V5cykuZm9yRWFjaCgoa2V5KSA9PiB7XG4gICAgICBpZiAodGhpcy5fa2V5c1trZXldIDw9IG5vdykge1xuICAgICAgICBkZWxldGUgdGhpcy5fa2V5c1trZXldO1xuICAgICAgfVxuICAgIH0pO1xuXG4gICAgdGhpcy5fYWRkZWRLZXlzQW1vdW50ID0gT2JqZWN0LmtleXModGhpcy5fa2V5cykubGVuZ3RoO1xuICB9XG5cbiAgLyoqXG4gICAqIEFkZCBuZXcgYmxvY2tlZCBrZXlcbiAgICpcbiAgICogQHBhcmFtIGtleSBTdHJpbmdcbiAgICogQHBhcmFtIHNlYyBOdW1iZXJcbiAgICovXG4gIGFkZChrZXksIHNlYykge1xuICAgIHRoaXMuYWRkTXMoa2V5LCBzZWMgKiAxMDAwKTtcbiAgfVxuXG4gIC8qKlxuICAgKiBBZGQgbmV3IGJsb2NrZWQga2V5IGZvciBtc1xuICAgKlxuICAgKiBAcGFyYW0ga2V5IFN0cmluZ1xuICAgKiBAcGFyYW0gbXMgTnVtYmVyXG4gICAqL1xuICBhZGRNcyhrZXksIG1zKSB7XG4gICAgdGhpcy5fa2V5c1trZXldID0gRGF0ZS5ub3coKSArIG1zO1xuICAgIHRoaXMuX2FkZGVkS2V5c0Ftb3VudCsrO1xuICAgIGlmICh0aGlzLl9hZGRlZEtleXNBbW91bnQgPiA5OTkpIHtcbiAgICAgIHRoaXMuY29sbGVjdEV4cGlyZWQoKTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogMCBtZWFucyBub3QgYmxvY2tlZFxuICAgKlxuICAgKiBAcGFyYW0ga2V5XG4gICAqIEByZXR1cm5zIHtudW1iZXJ9XG4gICAqL1xuICBtc0JlZm9yZUV4cGlyZShrZXkpIHtcbiAgICBjb25zdCBleHBpcmUgPSB0aGlzLl9rZXlzW2tleV07XG5cbiAgICBpZiAoZXhwaXJlICYmIGV4cGlyZSA+PSBEYXRlLm5vdygpKSB7XG4gICAgICB0aGlzLmNvbGxlY3RFeHBpcmVkKCk7XG4gICAgICBjb25zdCBub3cgPSBEYXRlLm5vdygpO1xuICAgICAgcmV0dXJuIGV4cGlyZSA+PSBub3cgPyBleHBpcmUgLSBub3cgOiAwO1xuICAgIH1cblxuICAgIHJldHVybiAwO1xuICB9XG5cbiAgLyoqXG4gICAqIElmIGtleSBpcyBub3QgZ2l2ZW4sIGRlbGV0ZSBhbGwgZGF0YSBpbiBtZW1vcnlcbiAgICogXG4gICAqIEBwYXJhbSB7c3RyaW5nfHVuZGVmaW5lZH0ga2V5XG4gICAqL1xuICBkZWxldGUoa2V5KSB7XG4gICAgaWYgKGtleSkge1xuICAgICAgZGVsZXRlIHRoaXMuX2tleXNba2V5XTtcbiAgICB9IGVsc2Uge1xuICAgICAgT2JqZWN0LmtleXModGhpcy5fa2V5cykuZm9yRWFjaCgoa2V5KSA9PiB7XG4gICAgICAgIGRlbGV0ZSB0aGlzLl9rZXlzW2tleV07XG4gICAgICB9KTtcbiAgICB9XG4gIH1cbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/rate-limiter-flexible/lib/component/BlockedKeys/BlockedKeys.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/rate-limiter-flexible/lib/component/BlockedKeys/index.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/rate-limiter-flexible/lib/component/BlockedKeys/index.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const BlockedKeys = __webpack_require__(/*! ./BlockedKeys */ \"(rsc)/./node_modules/rate-limiter-flexible/lib/component/BlockedKeys/BlockedKeys.js\");\n\nmodule.exports = BlockedKeys;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmF0ZS1saW1pdGVyLWZsZXhpYmxlL2xpYi9jb21wb25lbnQvQmxvY2tlZEtleXMvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUEsb0JBQW9CLG1CQUFPLENBQUMsMEdBQWU7O0FBRTNDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3Jzci1wcm9wZXJ0eS1tYW5hZ2VtZW50LWJhY2tlbmQvLi9ub2RlX21vZHVsZXMvcmF0ZS1saW1pdGVyLWZsZXhpYmxlL2xpYi9jb21wb25lbnQvQmxvY2tlZEtleXMvaW5kZXguanM/MGQxNiJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBCbG9ja2VkS2V5cyA9IHJlcXVpcmUoJy4vQmxvY2tlZEtleXMnKTtcblxubW9kdWxlLmV4cG9ydHMgPSBCbG9ja2VkS2V5cztcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/rate-limiter-flexible/lib/component/BlockedKeys/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/rate-limiter-flexible/lib/component/MemoryStorage/MemoryStorage.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/rate-limiter-flexible/lib/component/MemoryStorage/MemoryStorage.js ***!
  \*****************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const Record = __webpack_require__(/*! ./Record */ \"(rsc)/./node_modules/rate-limiter-flexible/lib/component/MemoryStorage/Record.js\");\nconst RateLimiterRes = __webpack_require__(/*! ../../RateLimiterRes */ \"(rsc)/./node_modules/rate-limiter-flexible/lib/RateLimiterRes.js\");\n\nmodule.exports = class MemoryStorage {\n  constructor() {\n    /**\n     * @type {Object.<string, Record>}\n     * @private\n     */\n    this._storage = {};\n  }\n\n  incrby(key, value, durationSec) {\n    if (this._storage[key]) {\n      const msBeforeExpires = this._storage[key].expiresAt\n        ? this._storage[key].expiresAt.getTime() - new Date().getTime()\n        : -1;\n      if (msBeforeExpires !== 0) {\n        // Change value\n        this._storage[key].value = this._storage[key].value + value;\n\n        return new RateLimiterRes(0, msBeforeExpires, this._storage[key].value, false);\n      }\n\n      return this.set(key, value, durationSec);\n    }\n    return this.set(key, value, durationSec);\n  }\n\n  set(key, value, durationSec) {\n    const durationMs = durationSec * 1000;\n\n    if (this._storage[key] && this._storage[key].timeoutId) {\n      clearTimeout(this._storage[key].timeoutId);\n    }\n\n    this._storage[key] = new Record(\n      value,\n      durationMs > 0 ? new Date(Date.now() + durationMs) : null\n    );\n    if (durationMs > 0) {\n      this._storage[key].timeoutId = setTimeout(() => {\n        delete this._storage[key];\n      }, durationMs);\n      if (this._storage[key].timeoutId.unref) {\n        this._storage[key].timeoutId.unref();\n      }\n    }\n\n    return new RateLimiterRes(0, durationMs === 0 ? -1 : durationMs, this._storage[key].value, true);\n  }\n\n  /**\n   *\n   * @param key\n   * @returns {*}\n   */\n  get(key) {\n    if (this._storage[key]) {\n      const msBeforeExpires = this._storage[key].expiresAt\n        ? this._storage[key].expiresAt.getTime() - new Date().getTime()\n        : -1;\n      return new RateLimiterRes(0, msBeforeExpires, this._storage[key].value, false);\n    }\n    return null;\n  }\n\n  /**\n   *\n   * @param key\n   * @returns {boolean}\n   */\n  delete(key) {\n    if (this._storage[key]) {\n      if (this._storage[key].timeoutId) {\n        clearTimeout(this._storage[key].timeoutId);\n      }\n      delete this._storage[key];\n      return true;\n    }\n    return false;\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/rate-limiter-flexible/lib/component/MemoryStorage/MemoryStorage.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/rate-limiter-flexible/lib/component/MemoryStorage/Record.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/rate-limiter-flexible/lib/component/MemoryStorage/Record.js ***!
  \**********************************************************************************/
/***/ ((module) => {

eval("module.exports = class Record {\n  /**\n   *\n   * @param value int\n   * @param expiresAt Date|int\n   * @param timeoutId\n   */\n  constructor(value, expiresAt, timeoutId = null) {\n    this.value = value;\n    this.expiresAt = expiresAt;\n    this.timeoutId = timeoutId;\n  }\n\n  get value() {\n    return this._value;\n  }\n\n  set value(value) {\n    this._value = parseInt(value);\n  }\n\n  get expiresAt() {\n    return this._expiresAt;\n  }\n\n  set expiresAt(value) {\n    if (!(value instanceof Date) && Number.isInteger(value)) {\n      value = new Date(value);\n    }\n    this._expiresAt = value;\n  }\n\n  get timeoutId() {\n    return this._timeoutId;\n  }\n\n  set timeoutId(value) {\n    this._timeoutId = value;\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmF0ZS1saW1pdGVyLWZsZXhpYmxlL2xpYi9jb21wb25lbnQvTWVtb3J5U3RvcmFnZS9SZWNvcmQuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zcnNyLXByb3BlcnR5LW1hbmFnZW1lbnQtYmFja2VuZC8uL25vZGVfbW9kdWxlcy9yYXRlLWxpbWl0ZXItZmxleGlibGUvbGliL2NvbXBvbmVudC9NZW1vcnlTdG9yYWdlL1JlY29yZC5qcz8zNGQ2Il0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gY2xhc3MgUmVjb3JkIHtcbiAgLyoqXG4gICAqXG4gICAqIEBwYXJhbSB2YWx1ZSBpbnRcbiAgICogQHBhcmFtIGV4cGlyZXNBdCBEYXRlfGludFxuICAgKiBAcGFyYW0gdGltZW91dElkXG4gICAqL1xuICBjb25zdHJ1Y3Rvcih2YWx1ZSwgZXhwaXJlc0F0LCB0aW1lb3V0SWQgPSBudWxsKSB7XG4gICAgdGhpcy52YWx1ZSA9IHZhbHVlO1xuICAgIHRoaXMuZXhwaXJlc0F0ID0gZXhwaXJlc0F0O1xuICAgIHRoaXMudGltZW91dElkID0gdGltZW91dElkO1xuICB9XG5cbiAgZ2V0IHZhbHVlKCkge1xuICAgIHJldHVybiB0aGlzLl92YWx1ZTtcbiAgfVxuXG4gIHNldCB2YWx1ZSh2YWx1ZSkge1xuICAgIHRoaXMuX3ZhbHVlID0gcGFyc2VJbnQodmFsdWUpO1xuICB9XG5cbiAgZ2V0IGV4cGlyZXNBdCgpIHtcbiAgICByZXR1cm4gdGhpcy5fZXhwaXJlc0F0O1xuICB9XG5cbiAgc2V0IGV4cGlyZXNBdCh2YWx1ZSkge1xuICAgIGlmICghKHZhbHVlIGluc3RhbmNlb2YgRGF0ZSkgJiYgTnVtYmVyLmlzSW50ZWdlcih2YWx1ZSkpIHtcbiAgICAgIHZhbHVlID0gbmV3IERhdGUodmFsdWUpO1xuICAgIH1cbiAgICB0aGlzLl9leHBpcmVzQXQgPSB2YWx1ZTtcbiAgfVxuXG4gIGdldCB0aW1lb3V0SWQoKSB7XG4gICAgcmV0dXJuIHRoaXMuX3RpbWVvdXRJZDtcbiAgfVxuXG4gIHNldCB0aW1lb3V0SWQodmFsdWUpIHtcbiAgICB0aGlzLl90aW1lb3V0SWQgPSB2YWx1ZTtcbiAgfVxufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/rate-limiter-flexible/lib/component/MemoryStorage/Record.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/rate-limiter-flexible/lib/component/RateLimiterQueueError.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/rate-limiter-flexible/lib/component/RateLimiterQueueError.js ***!
  \***********************************************************************************/
/***/ ((module) => {

eval("module.exports = class RateLimiterQueueError extends Error {\n  constructor(message, extra) {\n    super();\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, this.constructor);\n    }\n    this.name = 'CustomError';\n    this.message = message;\n    if (extra) {\n      this.extra = extra;\n    }\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmF0ZS1saW1pdGVyLWZsZXhpYmxlL2xpYi9jb21wb25lbnQvUmF0ZUxpbWl0ZXJRdWV1ZUVycm9yLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3Jzci1wcm9wZXJ0eS1tYW5hZ2VtZW50LWJhY2tlbmQvLi9ub2RlX21vZHVsZXMvcmF0ZS1saW1pdGVyLWZsZXhpYmxlL2xpYi9jb21wb25lbnQvUmF0ZUxpbWl0ZXJRdWV1ZUVycm9yLmpzPzQ3ODkiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSBjbGFzcyBSYXRlTGltaXRlclF1ZXVlRXJyb3IgZXh0ZW5kcyBFcnJvciB7XG4gIGNvbnN0cnVjdG9yKG1lc3NhZ2UsIGV4dHJhKSB7XG4gICAgc3VwZXIoKTtcbiAgICBpZiAoRXJyb3IuY2FwdHVyZVN0YWNrVHJhY2UpIHtcbiAgICAgIEVycm9yLmNhcHR1cmVTdGFja1RyYWNlKHRoaXMsIHRoaXMuY29uc3RydWN0b3IpO1xuICAgIH1cbiAgICB0aGlzLm5hbWUgPSAnQ3VzdG9tRXJyb3InO1xuICAgIHRoaXMubWVzc2FnZSA9IG1lc3NhZ2U7XG4gICAgaWYgKGV4dHJhKSB7XG4gICAgICB0aGlzLmV4dHJhID0gZXh0cmE7XG4gICAgfVxuICB9XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/rate-limiter-flexible/lib/component/RateLimiterQueueError.js\n");

/***/ })

};
;