/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/buffer-from";
exports.ids = ["vendor-chunks/buffer-from"];
exports.modules = {

/***/ "(rsc)/./node_modules/buffer-from/index.js":
/*!*******************************************!*\
  !*** ./node_modules/buffer-from/index.js ***!
  \*******************************************/
/***/ ((module) => {

eval("/* eslint-disable node/no-deprecated-api */\n\nvar toString = Object.prototype.toString\n\nvar isModern = (\n  typeof Buffer !== 'undefined' &&\n  typeof Buffer.alloc === 'function' &&\n  typeof Buffer.allocUnsafe === 'function' &&\n  typeof Buffer.from === 'function'\n)\n\nfunction isArrayBuffer (input) {\n  return toString.call(input).slice(8, -1) === 'ArrayBuffer'\n}\n\nfunction fromArrayBuffer (obj, byteOffset, length) {\n  byteOffset >>>= 0\n\n  var maxLength = obj.byteLength - byteOffset\n\n  if (maxLength < 0) {\n    throw new RangeError(\"'offset' is out of bounds\")\n  }\n\n  if (length === undefined) {\n    length = maxLength\n  } else {\n    length >>>= 0\n\n    if (length > maxLength) {\n      throw new RangeError(\"'length' is out of bounds\")\n    }\n  }\n\n  return isModern\n    ? Buffer.from(obj.slice(byteOffset, byteOffset + length))\n    : new Buffer(new Uint8Array(obj.slice(byteOffset, byteOffset + length)))\n}\n\nfunction fromString (string, encoding) {\n  if (typeof encoding !== 'string' || encoding === '') {\n    encoding = 'utf8'\n  }\n\n  if (!Buffer.isEncoding(encoding)) {\n    throw new TypeError('\"encoding\" must be a valid string encoding')\n  }\n\n  return isModern\n    ? Buffer.from(string, encoding)\n    : new Buffer(string, encoding)\n}\n\nfunction bufferFrom (value, encodingOrOffset, length) {\n  if (typeof value === 'number') {\n    throw new TypeError('\"value\" argument must not be a number')\n  }\n\n  if (isArrayBuffer(value)) {\n    return fromArrayBuffer(value, encodingOrOffset, length)\n  }\n\n  if (typeof value === 'string') {\n    return fromString(value, encodingOrOffset)\n  }\n\n  return isModern\n    ? Buffer.from(value)\n    : new Buffer(value)\n}\n\nmodule.exports = bufferFrom\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/buffer-from/index.js\n");

/***/ })

};
;