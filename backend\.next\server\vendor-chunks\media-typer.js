/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/media-typer";
exports.ids = ["vendor-chunks/media-typer"];
exports.modules = {

/***/ "(rsc)/./node_modules/media-typer/index.js":
/*!*******************************************!*\
  !*** ./node_modules/media-typer/index.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("/*!\n * media-typer\n * Copyright(c) 2014 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n/**\n * RegExp to match *( \";\" parameter ) in RFC 2616 sec 3.7\n *\n * parameter     = token \"=\" ( token | quoted-string )\n * token         = 1*<any CHAR except CTLs or separators>\n * separators    = \"(\" | \")\" | \"<\" | \">\" | \"@\"\n *               | \",\" | \";\" | \":\" | \"\\\" | <\">\n *               | \"/\" | \"[\" | \"]\" | \"?\" | \"=\"\n *               | \"{\" | \"}\" | SP | HT\n * quoted-string = ( <\"> *(qdtext | quoted-pair ) <\"> )\n * qdtext        = <any TEXT except <\">>\n * quoted-pair   = \"\\\" CHAR\n * CHAR          = <any US-ASCII character (octets 0 - 127)>\n * TEXT          = <any OCTET except CTLs, but including LWS>\n * LWS           = [CRLF] 1*( SP | HT )\n * CRLF          = CR LF\n * CR            = <US-ASCII CR, carriage return (13)>\n * LF            = <US-ASCII LF, linefeed (10)>\n * SP            = <US-ASCII SP, space (32)>\n * SHT           = <US-ASCII HT, horizontal-tab (9)>\n * CTL           = <any US-ASCII control character (octets 0 - 31) and DEL (127)>\n * OCTET         = <any 8-bit sequence of data>\n */\nvar paramRegExp = /; *([!#$%&'\\*\\+\\-\\.0-9A-Z\\^_`a-z\\|~]+) *= *(\"(?:[ !\\u0023-\\u005b\\u005d-\\u007e\\u0080-\\u00ff]|\\\\[\\u0020-\\u007e])*\"|[!#$%&'\\*\\+\\-\\.0-9A-Z\\^_`a-z\\|~]+) */g;\nvar textRegExp = /^[\\u0020-\\u007e\\u0080-\\u00ff]+$/\nvar tokenRegExp = /^[!#$%&'\\*\\+\\-\\.0-9A-Z\\^_`a-z\\|~]+$/\n\n/**\n * RegExp to match quoted-pair in RFC 2616\n *\n * quoted-pair = \"\\\" CHAR\n * CHAR        = <any US-ASCII character (octets 0 - 127)>\n */\nvar qescRegExp = /\\\\([\\u0000-\\u007f])/g;\n\n/**\n * RegExp to match chars that must be quoted-pair in RFC 2616\n */\nvar quoteRegExp = /([\\\\\"])/g;\n\n/**\n * RegExp to match type in RFC 6838\n *\n * type-name = restricted-name\n * subtype-name = restricted-name\n * restricted-name = restricted-name-first *126restricted-name-chars\n * restricted-name-first  = ALPHA / DIGIT\n * restricted-name-chars  = ALPHA / DIGIT / \"!\" / \"#\" /\n *                          \"$\" / \"&\" / \"-\" / \"^\" / \"_\"\n * restricted-name-chars =/ \".\" ; Characters before first dot always\n *                              ; specify a facet name\n * restricted-name-chars =/ \"+\" ; Characters after last plus always\n *                              ; specify a structured syntax suffix\n * ALPHA =  %x41-5A / %x61-7A   ; A-Z / a-z\n * DIGIT =  %x30-39             ; 0-9\n */\nvar subtypeNameRegExp = /^[A-Za-z0-9][A-Za-z0-9!#$&^_.-]{0,126}$/\nvar typeNameRegExp = /^[A-Za-z0-9][A-Za-z0-9!#$&^_-]{0,126}$/\nvar typeRegExp = /^ *([A-Za-z0-9][A-Za-z0-9!#$&^_-]{0,126})\\/([A-Za-z0-9][A-Za-z0-9!#$&^_.+-]{0,126}) *$/;\n\n/**\n * Module exports.\n */\n\nexports.format = format\nexports.parse = parse\n\n/**\n * Format object to media type.\n *\n * @param {object} obj\n * @return {string}\n * @api public\n */\n\nfunction format(obj) {\n  if (!obj || typeof obj !== 'object') {\n    throw new TypeError('argument obj is required')\n  }\n\n  var parameters = obj.parameters\n  var subtype = obj.subtype\n  var suffix = obj.suffix\n  var type = obj.type\n\n  if (!type || !typeNameRegExp.test(type)) {\n    throw new TypeError('invalid type')\n  }\n\n  if (!subtype || !subtypeNameRegExp.test(subtype)) {\n    throw new TypeError('invalid subtype')\n  }\n\n  // format as type/subtype\n  var string = type + '/' + subtype\n\n  // append +suffix\n  if (suffix) {\n    if (!typeNameRegExp.test(suffix)) {\n      throw new TypeError('invalid suffix')\n    }\n\n    string += '+' + suffix\n  }\n\n  // append parameters\n  if (parameters && typeof parameters === 'object') {\n    var param\n    var params = Object.keys(parameters).sort()\n\n    for (var i = 0; i < params.length; i++) {\n      param = params[i]\n\n      if (!tokenRegExp.test(param)) {\n        throw new TypeError('invalid parameter name')\n      }\n\n      string += '; ' + param + '=' + qstring(parameters[param])\n    }\n  }\n\n  return string\n}\n\n/**\n * Parse media type to object.\n *\n * @param {string|object} string\n * @return {Object}\n * @api public\n */\n\nfunction parse(string) {\n  if (!string) {\n    throw new TypeError('argument string is required')\n  }\n\n  // support req/res-like objects as argument\n  if (typeof string === 'object') {\n    string = getcontenttype(string)\n  }\n\n  if (typeof string !== 'string') {\n    throw new TypeError('argument string is required to be a string')\n  }\n\n  var index = string.indexOf(';')\n  var type = index !== -1\n    ? string.substr(0, index)\n    : string\n\n  var key\n  var match\n  var obj = splitType(type)\n  var params = {}\n  var value\n\n  paramRegExp.lastIndex = index\n\n  while (match = paramRegExp.exec(string)) {\n    if (match.index !== index) {\n      throw new TypeError('invalid parameter format')\n    }\n\n    index += match[0].length\n    key = match[1].toLowerCase()\n    value = match[2]\n\n    if (value[0] === '\"') {\n      // remove quotes and escapes\n      value = value\n        .substr(1, value.length - 2)\n        .replace(qescRegExp, '$1')\n    }\n\n    params[key] = value\n  }\n\n  if (index !== -1 && index !== string.length) {\n    throw new TypeError('invalid parameter format')\n  }\n\n  obj.parameters = params\n\n  return obj\n}\n\n/**\n * Get content-type from req/res objects.\n *\n * @param {object}\n * @return {Object}\n * @api private\n */\n\nfunction getcontenttype(obj) {\n  if (typeof obj.getHeader === 'function') {\n    // res-like\n    return obj.getHeader('content-type')\n  }\n\n  if (typeof obj.headers === 'object') {\n    // req-like\n    return obj.headers && obj.headers['content-type']\n  }\n}\n\n/**\n * Quote a string if necessary.\n *\n * @param {string} val\n * @return {string}\n * @api private\n */\n\nfunction qstring(val) {\n  var str = String(val)\n\n  // no need to quote tokens\n  if (tokenRegExp.test(str)) {\n    return str\n  }\n\n  if (str.length > 0 && !textRegExp.test(str)) {\n    throw new TypeError('invalid parameter value')\n  }\n\n  return '\"' + str.replace(quoteRegExp, '\\\\$1') + '\"'\n}\n\n/**\n * Simply \"type/subtype+siffx\" into parts.\n *\n * @param {string} string\n * @return {Object}\n * @api private\n */\n\nfunction splitType(string) {\n  var match = typeRegExp.exec(string.toLowerCase())\n\n  if (!match) {\n    throw new TypeError('invalid media type')\n  }\n\n  var type = match[1]\n  var subtype = match[2]\n  var suffix\n\n  // suffix after last +\n  var index = subtype.lastIndexOf('+')\n  if (index !== -1) {\n    suffix = subtype.substr(index + 1)\n    subtype = subtype.substr(0, index)\n  }\n\n  var obj = {\n    type: type,\n    subtype: subtype,\n    suffix: suffix\n  }\n\n  return obj\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/media-typer/index.js\n");

/***/ })

};
;