"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/negotiator";
exports.ids = ["vendor-chunks/negotiator"];
exports.modules = {

/***/ "(rsc)/./node_modules/negotiator/index.js":
/*!******************************************!*\
  !*** ./node_modules/negotiator/index.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/*!\n * negotiator\n * Copyright(c) 2012 Federico Romero\n * Copyright(c) 2012-2014 Isaac Z. Schlueter\n * Copyright(c) 2015 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\nvar preferredCharsets = __webpack_require__(/*! ./lib/charset */ \"(rsc)/./node_modules/negotiator/lib/charset.js\")\nvar preferredEncodings = __webpack_require__(/*! ./lib/encoding */ \"(rsc)/./node_modules/negotiator/lib/encoding.js\")\nvar preferredLanguages = __webpack_require__(/*! ./lib/language */ \"(rsc)/./node_modules/negotiator/lib/language.js\")\nvar preferredMediaTypes = __webpack_require__(/*! ./lib/mediaType */ \"(rsc)/./node_modules/negotiator/lib/mediaType.js\")\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = Negotiator;\nmodule.exports.Negotiator = Negotiator;\n\n/**\n * Create a Negotiator instance from a request.\n * @param {object} request\n * @public\n */\n\nfunction Negotiator(request) {\n  if (!(this instanceof Negotiator)) {\n    return new Negotiator(request);\n  }\n\n  this.request = request;\n}\n\nNegotiator.prototype.charset = function charset(available) {\n  var set = this.charsets(available);\n  return set && set[0];\n};\n\nNegotiator.prototype.charsets = function charsets(available) {\n  return preferredCharsets(this.request.headers['accept-charset'], available);\n};\n\nNegotiator.prototype.encoding = function encoding(available, preferred) {\n  var set = this.encodings(available, preferred);\n  return set && set[0];\n};\n\nNegotiator.prototype.encodings = function encodings(available, preferred) {\n  return preferredEncodings(this.request.headers['accept-encoding'], available, preferred);\n};\n\nNegotiator.prototype.language = function language(available) {\n  var set = this.languages(available);\n  return set && set[0];\n};\n\nNegotiator.prototype.languages = function languages(available) {\n  return preferredLanguages(this.request.headers['accept-language'], available);\n};\n\nNegotiator.prototype.mediaType = function mediaType(available) {\n  var set = this.mediaTypes(available);\n  return set && set[0];\n};\n\nNegotiator.prototype.mediaTypes = function mediaTypes(available) {\n  return preferredMediaTypes(this.request.headers.accept, available);\n};\n\n// Backwards compatibility\nNegotiator.prototype.preferredCharset = Negotiator.prototype.charset;\nNegotiator.prototype.preferredCharsets = Negotiator.prototype.charsets;\nNegotiator.prototype.preferredEncoding = Negotiator.prototype.encoding;\nNegotiator.prototype.preferredEncodings = Negotiator.prototype.encodings;\nNegotiator.prototype.preferredLanguage = Negotiator.prototype.language;\nNegotiator.prototype.preferredLanguages = Negotiator.prototype.languages;\nNegotiator.prototype.preferredMediaType = Negotiator.prototype.mediaType;\nNegotiator.prototype.preferredMediaTypes = Negotiator.prototype.mediaTypes;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/negotiator/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/negotiator/lib/charset.js":
/*!************************************************!*\
  !*** ./node_modules/negotiator/lib/charset.js ***!
  \************************************************/
/***/ ((module) => {

eval("/**\n * negotiator\n * Copyright(c) 2012 Isaac Z. Schlueter\n * Copyright(c) 2014 Federico Romero\n * Copyright(c) 2014-2015 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = preferredCharsets;\nmodule.exports.preferredCharsets = preferredCharsets;\n\n/**\n * Module variables.\n * @private\n */\n\nvar simpleCharsetRegExp = /^\\s*([^\\s;]+)\\s*(?:;(.*))?$/;\n\n/**\n * Parse the Accept-Charset header.\n * @private\n */\n\nfunction parseAcceptCharset(accept) {\n  var accepts = accept.split(',');\n\n  for (var i = 0, j = 0; i < accepts.length; i++) {\n    var charset = parseCharset(accepts[i].trim(), i);\n\n    if (charset) {\n      accepts[j++] = charset;\n    }\n  }\n\n  // trim accepts\n  accepts.length = j;\n\n  return accepts;\n}\n\n/**\n * Parse a charset from the Accept-Charset header.\n * @private\n */\n\nfunction parseCharset(str, i) {\n  var match = simpleCharsetRegExp.exec(str);\n  if (!match) return null;\n\n  var charset = match[1];\n  var q = 1;\n  if (match[2]) {\n    var params = match[2].split(';')\n    for (var j = 0; j < params.length; j++) {\n      var p = params[j].trim().split('=');\n      if (p[0] === 'q') {\n        q = parseFloat(p[1]);\n        break;\n      }\n    }\n  }\n\n  return {\n    charset: charset,\n    q: q,\n    i: i\n  };\n}\n\n/**\n * Get the priority of a charset.\n * @private\n */\n\nfunction getCharsetPriority(charset, accepted, index) {\n  var priority = {o: -1, q: 0, s: 0};\n\n  for (var i = 0; i < accepted.length; i++) {\n    var spec = specify(charset, accepted[i], index);\n\n    if (spec && (priority.s - spec.s || priority.q - spec.q || priority.o - spec.o) < 0) {\n      priority = spec;\n    }\n  }\n\n  return priority;\n}\n\n/**\n * Get the specificity of the charset.\n * @private\n */\n\nfunction specify(charset, spec, index) {\n  var s = 0;\n  if(spec.charset.toLowerCase() === charset.toLowerCase()){\n    s |= 1;\n  } else if (spec.charset !== '*' ) {\n    return null\n  }\n\n  return {\n    i: index,\n    o: spec.i,\n    q: spec.q,\n    s: s\n  }\n}\n\n/**\n * Get the preferred charsets from an Accept-Charset header.\n * @public\n */\n\nfunction preferredCharsets(accept, provided) {\n  // RFC 2616 sec 14.2: no header = *\n  var accepts = parseAcceptCharset(accept === undefined ? '*' : accept || '');\n\n  if (!provided) {\n    // sorted list of all charsets\n    return accepts\n      .filter(isQuality)\n      .sort(compareSpecs)\n      .map(getFullCharset);\n  }\n\n  var priorities = provided.map(function getPriority(type, index) {\n    return getCharsetPriority(type, accepts, index);\n  });\n\n  // sorted list of accepted charsets\n  return priorities.filter(isQuality).sort(compareSpecs).map(function getCharset(priority) {\n    return provided[priorities.indexOf(priority)];\n  });\n}\n\n/**\n * Compare two specs.\n * @private\n */\n\nfunction compareSpecs(a, b) {\n  return (b.q - a.q) || (b.s - a.s) || (a.o - b.o) || (a.i - b.i) || 0;\n}\n\n/**\n * Get full charset string.\n * @private\n */\n\nfunction getFullCharset(spec) {\n  return spec.charset;\n}\n\n/**\n * Check if a spec has any quality.\n * @private\n */\n\nfunction isQuality(spec) {\n  return spec.q > 0;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/negotiator/lib/charset.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/negotiator/lib/encoding.js":
/*!*************************************************!*\
  !*** ./node_modules/negotiator/lib/encoding.js ***!
  \*************************************************/
/***/ ((module) => {

eval("/**\n * negotiator\n * Copyright(c) 2012 Isaac Z. Schlueter\n * Copyright(c) 2014 Federico Romero\n * Copyright(c) 2014-2015 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = preferredEncodings;\nmodule.exports.preferredEncodings = preferredEncodings;\n\n/**\n * Module variables.\n * @private\n */\n\nvar simpleEncodingRegExp = /^\\s*([^\\s;]+)\\s*(?:;(.*))?$/;\n\n/**\n * Parse the Accept-Encoding header.\n * @private\n */\n\nfunction parseAcceptEncoding(accept) {\n  var accepts = accept.split(',');\n  var hasIdentity = false;\n  var minQuality = 1;\n\n  for (var i = 0, j = 0; i < accepts.length; i++) {\n    var encoding = parseEncoding(accepts[i].trim(), i);\n\n    if (encoding) {\n      accepts[j++] = encoding;\n      hasIdentity = hasIdentity || specify('identity', encoding);\n      minQuality = Math.min(minQuality, encoding.q || 1);\n    }\n  }\n\n  if (!hasIdentity) {\n    /*\n     * If identity doesn't explicitly appear in the accept-encoding header,\n     * it's added to the list of acceptable encoding with the lowest q\n     */\n    accepts[j++] = {\n      encoding: 'identity',\n      q: minQuality,\n      i: i\n    };\n  }\n\n  // trim accepts\n  accepts.length = j;\n\n  return accepts;\n}\n\n/**\n * Parse an encoding from the Accept-Encoding header.\n * @private\n */\n\nfunction parseEncoding(str, i) {\n  var match = simpleEncodingRegExp.exec(str);\n  if (!match) return null;\n\n  var encoding = match[1];\n  var q = 1;\n  if (match[2]) {\n    var params = match[2].split(';');\n    for (var j = 0; j < params.length; j++) {\n      var p = params[j].trim().split('=');\n      if (p[0] === 'q') {\n        q = parseFloat(p[1]);\n        break;\n      }\n    }\n  }\n\n  return {\n    encoding: encoding,\n    q: q,\n    i: i\n  };\n}\n\n/**\n * Get the priority of an encoding.\n * @private\n */\n\nfunction getEncodingPriority(encoding, accepted, index) {\n  var priority = {encoding: encoding, o: -1, q: 0, s: 0};\n\n  for (var i = 0; i < accepted.length; i++) {\n    var spec = specify(encoding, accepted[i], index);\n\n    if (spec && (priority.s - spec.s || priority.q - spec.q || priority.o - spec.o) < 0) {\n      priority = spec;\n    }\n  }\n\n  return priority;\n}\n\n/**\n * Get the specificity of the encoding.\n * @private\n */\n\nfunction specify(encoding, spec, index) {\n  var s = 0;\n  if(spec.encoding.toLowerCase() === encoding.toLowerCase()){\n    s |= 1;\n  } else if (spec.encoding !== '*' ) {\n    return null\n  }\n\n  return {\n    encoding: encoding,\n    i: index,\n    o: spec.i,\n    q: spec.q,\n    s: s\n  }\n};\n\n/**\n * Get the preferred encodings from an Accept-Encoding header.\n * @public\n */\n\nfunction preferredEncodings(accept, provided, preferred) {\n  var accepts = parseAcceptEncoding(accept || '');\n\n  var comparator = preferred ? function comparator (a, b) {\n    if (a.q !== b.q) {\n      return b.q - a.q // higher quality first\n    }\n\n    var aPreferred = preferred.indexOf(a.encoding)\n    var bPreferred = preferred.indexOf(b.encoding)\n\n    if (aPreferred === -1 && bPreferred === -1) {\n      // consider the original specifity/order\n      return (b.s - a.s) || (a.o - b.o) || (a.i - b.i)\n    }\n\n    if (aPreferred !== -1 && bPreferred !== -1) {\n      return aPreferred - bPreferred // consider the preferred order\n    }\n\n    return aPreferred === -1 ? 1 : -1 // preferred first\n  } : compareSpecs;\n\n  if (!provided) {\n    // sorted list of all encodings\n    return accepts\n      .filter(isQuality)\n      .sort(comparator)\n      .map(getFullEncoding);\n  }\n\n  var priorities = provided.map(function getPriority(type, index) {\n    return getEncodingPriority(type, accepts, index);\n  });\n\n  // sorted list of accepted encodings\n  return priorities.filter(isQuality).sort(comparator).map(function getEncoding(priority) {\n    return provided[priorities.indexOf(priority)];\n  });\n}\n\n/**\n * Compare two specs.\n * @private\n */\n\nfunction compareSpecs(a, b) {\n  return (b.q - a.q) || (b.s - a.s) || (a.o - b.o) || (a.i - b.i);\n}\n\n/**\n * Get full encoding string.\n * @private\n */\n\nfunction getFullEncoding(spec) {\n  return spec.encoding;\n}\n\n/**\n * Check if a spec has any quality.\n * @private\n */\n\nfunction isQuality(spec) {\n  return spec.q > 0;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/negotiator/lib/encoding.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/negotiator/lib/language.js":
/*!*************************************************!*\
  !*** ./node_modules/negotiator/lib/language.js ***!
  \*************************************************/
/***/ ((module) => {

eval("/**\n * negotiator\n * Copyright(c) 2012 Isaac Z. Schlueter\n * Copyright(c) 2014 Federico Romero\n * Copyright(c) 2014-2015 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = preferredLanguages;\nmodule.exports.preferredLanguages = preferredLanguages;\n\n/**\n * Module variables.\n * @private\n */\n\nvar simpleLanguageRegExp = /^\\s*([^\\s\\-;]+)(?:-([^\\s;]+))?\\s*(?:;(.*))?$/;\n\n/**\n * Parse the Accept-Language header.\n * @private\n */\n\nfunction parseAcceptLanguage(accept) {\n  var accepts = accept.split(',');\n\n  for (var i = 0, j = 0; i < accepts.length; i++) {\n    var language = parseLanguage(accepts[i].trim(), i);\n\n    if (language) {\n      accepts[j++] = language;\n    }\n  }\n\n  // trim accepts\n  accepts.length = j;\n\n  return accepts;\n}\n\n/**\n * Parse a language from the Accept-Language header.\n * @private\n */\n\nfunction parseLanguage(str, i) {\n  var match = simpleLanguageRegExp.exec(str);\n  if (!match) return null;\n\n  var prefix = match[1]\n  var suffix = match[2]\n  var full = prefix\n\n  if (suffix) full += \"-\" + suffix;\n\n  var q = 1;\n  if (match[3]) {\n    var params = match[3].split(';')\n    for (var j = 0; j < params.length; j++) {\n      var p = params[j].split('=');\n      if (p[0] === 'q') q = parseFloat(p[1]);\n    }\n  }\n\n  return {\n    prefix: prefix,\n    suffix: suffix,\n    q: q,\n    i: i,\n    full: full\n  };\n}\n\n/**\n * Get the priority of a language.\n * @private\n */\n\nfunction getLanguagePriority(language, accepted, index) {\n  var priority = {o: -1, q: 0, s: 0};\n\n  for (var i = 0; i < accepted.length; i++) {\n    var spec = specify(language, accepted[i], index);\n\n    if (spec && (priority.s - spec.s || priority.q - spec.q || priority.o - spec.o) < 0) {\n      priority = spec;\n    }\n  }\n\n  return priority;\n}\n\n/**\n * Get the specificity of the language.\n * @private\n */\n\nfunction specify(language, spec, index) {\n  var p = parseLanguage(language)\n  if (!p) return null;\n  var s = 0;\n  if(spec.full.toLowerCase() === p.full.toLowerCase()){\n    s |= 4;\n  } else if (spec.prefix.toLowerCase() === p.full.toLowerCase()) {\n    s |= 2;\n  } else if (spec.full.toLowerCase() === p.prefix.toLowerCase()) {\n    s |= 1;\n  } else if (spec.full !== '*' ) {\n    return null\n  }\n\n  return {\n    i: index,\n    o: spec.i,\n    q: spec.q,\n    s: s\n  }\n};\n\n/**\n * Get the preferred languages from an Accept-Language header.\n * @public\n */\n\nfunction preferredLanguages(accept, provided) {\n  // RFC 2616 sec 14.4: no header = *\n  var accepts = parseAcceptLanguage(accept === undefined ? '*' : accept || '');\n\n  if (!provided) {\n    // sorted list of all languages\n    return accepts\n      .filter(isQuality)\n      .sort(compareSpecs)\n      .map(getFullLanguage);\n  }\n\n  var priorities = provided.map(function getPriority(type, index) {\n    return getLanguagePriority(type, accepts, index);\n  });\n\n  // sorted list of accepted languages\n  return priorities.filter(isQuality).sort(compareSpecs).map(function getLanguage(priority) {\n    return provided[priorities.indexOf(priority)];\n  });\n}\n\n/**\n * Compare two specs.\n * @private\n */\n\nfunction compareSpecs(a, b) {\n  return (b.q - a.q) || (b.s - a.s) || (a.o - b.o) || (a.i - b.i) || 0;\n}\n\n/**\n * Get full language string.\n * @private\n */\n\nfunction getFullLanguage(spec) {\n  return spec.full;\n}\n\n/**\n * Check if a spec has any quality.\n * @private\n */\n\nfunction isQuality(spec) {\n  return spec.q > 0;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/negotiator/lib/language.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/negotiator/lib/mediaType.js":
/*!**************************************************!*\
  !*** ./node_modules/negotiator/lib/mediaType.js ***!
  \**************************************************/
/***/ ((module) => {

eval("/**\n * negotiator\n * Copyright(c) 2012 Isaac Z. Schlueter\n * Copyright(c) 2014 Federico Romero\n * Copyright(c) 2014-2015 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = preferredMediaTypes;\nmodule.exports.preferredMediaTypes = preferredMediaTypes;\n\n/**\n * Module variables.\n * @private\n */\n\nvar simpleMediaTypeRegExp = /^\\s*([^\\s\\/;]+)\\/([^;\\s]+)\\s*(?:;(.*))?$/;\n\n/**\n * Parse the Accept header.\n * @private\n */\n\nfunction parseAccept(accept) {\n  var accepts = splitMediaTypes(accept);\n\n  for (var i = 0, j = 0; i < accepts.length; i++) {\n    var mediaType = parseMediaType(accepts[i].trim(), i);\n\n    if (mediaType) {\n      accepts[j++] = mediaType;\n    }\n  }\n\n  // trim accepts\n  accepts.length = j;\n\n  return accepts;\n}\n\n/**\n * Parse a media type from the Accept header.\n * @private\n */\n\nfunction parseMediaType(str, i) {\n  var match = simpleMediaTypeRegExp.exec(str);\n  if (!match) return null;\n\n  var params = Object.create(null);\n  var q = 1;\n  var subtype = match[2];\n  var type = match[1];\n\n  if (match[3]) {\n    var kvps = splitParameters(match[3]).map(splitKeyValuePair);\n\n    for (var j = 0; j < kvps.length; j++) {\n      var pair = kvps[j];\n      var key = pair[0].toLowerCase();\n      var val = pair[1];\n\n      // get the value, unwrapping quotes\n      var value = val && val[0] === '\"' && val[val.length - 1] === '\"'\n        ? val.slice(1, -1)\n        : val;\n\n      if (key === 'q') {\n        q = parseFloat(value);\n        break;\n      }\n\n      // store parameter\n      params[key] = value;\n    }\n  }\n\n  return {\n    type: type,\n    subtype: subtype,\n    params: params,\n    q: q,\n    i: i\n  };\n}\n\n/**\n * Get the priority of a media type.\n * @private\n */\n\nfunction getMediaTypePriority(type, accepted, index) {\n  var priority = {o: -1, q: 0, s: 0};\n\n  for (var i = 0; i < accepted.length; i++) {\n    var spec = specify(type, accepted[i], index);\n\n    if (spec && (priority.s - spec.s || priority.q - spec.q || priority.o - spec.o) < 0) {\n      priority = spec;\n    }\n  }\n\n  return priority;\n}\n\n/**\n * Get the specificity of the media type.\n * @private\n */\n\nfunction specify(type, spec, index) {\n  var p = parseMediaType(type);\n  var s = 0;\n\n  if (!p) {\n    return null;\n  }\n\n  if(spec.type.toLowerCase() == p.type.toLowerCase()) {\n    s |= 4\n  } else if(spec.type != '*') {\n    return null;\n  }\n\n  if(spec.subtype.toLowerCase() == p.subtype.toLowerCase()) {\n    s |= 2\n  } else if(spec.subtype != '*') {\n    return null;\n  }\n\n  var keys = Object.keys(spec.params);\n  if (keys.length > 0) {\n    if (keys.every(function (k) {\n      return spec.params[k] == '*' || (spec.params[k] || '').toLowerCase() == (p.params[k] || '').toLowerCase();\n    })) {\n      s |= 1\n    } else {\n      return null\n    }\n  }\n\n  return {\n    i: index,\n    o: spec.i,\n    q: spec.q,\n    s: s,\n  }\n}\n\n/**\n * Get the preferred media types from an Accept header.\n * @public\n */\n\nfunction preferredMediaTypes(accept, provided) {\n  // RFC 2616 sec 14.2: no header = */*\n  var accepts = parseAccept(accept === undefined ? '*/*' : accept || '');\n\n  if (!provided) {\n    // sorted list of all types\n    return accepts\n      .filter(isQuality)\n      .sort(compareSpecs)\n      .map(getFullType);\n  }\n\n  var priorities = provided.map(function getPriority(type, index) {\n    return getMediaTypePriority(type, accepts, index);\n  });\n\n  // sorted list of accepted types\n  return priorities.filter(isQuality).sort(compareSpecs).map(function getType(priority) {\n    return provided[priorities.indexOf(priority)];\n  });\n}\n\n/**\n * Compare two specs.\n * @private\n */\n\nfunction compareSpecs(a, b) {\n  return (b.q - a.q) || (b.s - a.s) || (a.o - b.o) || (a.i - b.i) || 0;\n}\n\n/**\n * Get full type string.\n * @private\n */\n\nfunction getFullType(spec) {\n  return spec.type + '/' + spec.subtype;\n}\n\n/**\n * Check if a spec has any quality.\n * @private\n */\n\nfunction isQuality(spec) {\n  return spec.q > 0;\n}\n\n/**\n * Count the number of quotes in a string.\n * @private\n */\n\nfunction quoteCount(string) {\n  var count = 0;\n  var index = 0;\n\n  while ((index = string.indexOf('\"', index)) !== -1) {\n    count++;\n    index++;\n  }\n\n  return count;\n}\n\n/**\n * Split a key value pair.\n * @private\n */\n\nfunction splitKeyValuePair(str) {\n  var index = str.indexOf('=');\n  var key;\n  var val;\n\n  if (index === -1) {\n    key = str;\n  } else {\n    key = str.slice(0, index);\n    val = str.slice(index + 1);\n  }\n\n  return [key, val];\n}\n\n/**\n * Split an Accept header into media types.\n * @private\n */\n\nfunction splitMediaTypes(accept) {\n  var accepts = accept.split(',');\n\n  for (var i = 1, j = 0; i < accepts.length; i++) {\n    if (quoteCount(accepts[j]) % 2 == 0) {\n      accepts[++j] = accepts[i];\n    } else {\n      accepts[j] += ',' + accepts[i];\n    }\n  }\n\n  // trim accepts\n  accepts.length = j + 1;\n\n  return accepts;\n}\n\n/**\n * Split a string of parameters.\n * @private\n */\n\nfunction splitParameters(str) {\n  var parameters = str.split(';');\n\n  for (var i = 1, j = 0; i < parameters.length; i++) {\n    if (quoteCount(parameters[j]) % 2 == 0) {\n      parameters[++j] = parameters[i];\n    } else {\n      parameters[j] += ';' + parameters[i];\n    }\n  }\n\n  // trim parameters\n  parameters.length = j + 1;\n\n  for (var i = 0; i < parameters.length; i++) {\n    parameters[i] = parameters[i].trim();\n  }\n\n  return parameters;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/negotiator/lib/mediaType.js\n");

/***/ })

};
;